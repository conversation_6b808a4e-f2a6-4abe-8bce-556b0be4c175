2025-08-26 16:35:30.159654: 61
2025-08-26 16:35:30.162644: ✅ 数据库更新成功: planConfig
2025-08-26 16:35:30.163642: ✅ 数据库更新成功: SysConfig
2025-08-26 16:35:30.163642: ✅ 数据库更新成功: Sip2Config
2025-08-26 16:35:30.163642: ✅ 数据库更新成功: pageConfig
2025-08-26 16:35:30.163642: ✅ 数据库更新成功: readerConfig
2025-08-26 16:35:30.163642: ✅ 数据库更新成功: banZhengConfig
2025-08-26 16:35:30.163642: ✅ 数据库更新成功: printConfig
2025-08-26 16:35:30.378928: 开始初始化闸机协调器...
2025-08-26 16:35:30.379925: ✅ 已清除串口配置缓存，下次访问将重新从数据库读取
2025-08-26 16:35:30.380921: ✅ 已清除 SettingProvider 串口配置缓存
2025-08-26 16:35:30.397864: ✅ 通过 getSerialConfig 获取串口配置: COM1 @ 115200
2025-08-26 16:35:30.397864: ✅ 串口配置加载完成: COM1 @ 115200
2025-08-26 16:35:30.397864: 可用串口: [COM1, COM2, COM3, COM4, COM5, COM6]
2025-08-26 16:35:30.401860: 连接闸机串口: COM1
2025-08-26 16:35:30.402848: 尝试连接串口: COM1, 波特率: 115200
2025-08-26 16:35:30.402848: 串口连接成功: COM1 at 115200 baud
2025-08-26 16:35:30.402848: 开始监听串口数据
2025-08-26 16:35:30.402848: 串口连接状态变化: true
2025-08-26 16:35:30.402848: 闸机串口连接成功
2025-08-26 16:35:30.402848: 串口 COM1 连接成功 (波特率: 115200)
2025-08-26 16:35:30.403845: 闸机串口服务初始化成功: COM1
2025-08-26 16:35:30.403845: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-26 16:35:30.403845: 开始监听闸机串口命令
2025-08-26 16:35:30.403845: 开始初始化RFID服务和共享池...
2025-08-26 16:35:30.403845: 开始初始化增强RFID服务...
2025-08-26 16:35:30.403845: 开始初始化增强RFID服务...
2025-08-26 16:35:30.403845: SIP2图书信息服务初始化完成
2025-08-26 16:35:30.403845: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-26 16:35:30.404841: 📋 从数据库读取主从机配置: channel_1
2025-08-26 16:35:30.404841: 📋 配置详情: 主机模式
2025-08-26 16:35:30.404841: 🚀 主机模式：启动RFID硬件持续扫描
2025-08-26 16:35:30.405838: 启动RFID持续扫描...
2025-08-26 16:35:30.405838: changeReaders
2025-08-26 16:35:30.405838: createIsolate isOpen:false,isOpening:false
2025-08-26 16:35:30.406835: createIsolate newport null
2025-08-26 16:35:30.432750: socket 连接成功,isBroadcast:false
2025-08-26 16:35:30.432750: changeSocketStatus:true
2025-08-26 16:35:30.433754: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-26 16:35:30.433754: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-26 16:35:30.544381: Rsp : 941AY1AZFDFC
2025-08-26 16:35:30.552353: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-26 16:35:30.553351: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-26 16:35:30.554357: 发送心跳
2025-08-26 16:35:30.554357: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-26 16:35:30.624115: Rsp : 98YYYNNN00500320250826    1635232.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD51D
2025-08-26 16:35:30.907176: 找到网口配置: LSGate图书馆安全门RFID阅读器 - **************:6012
2025-08-26 16:35:30.908175: 使用网口连接: **************:6012
2025-08-26 16:35:30.908175: open():SendPort
2025-08-26 16:35:30.909172: untilDetcted():SendPort
2025-08-26 16:35:30.909172: 网口连接成功: **************:6012
2025-08-26 16:35:30.910167: startInventory():SendPort
2025-08-26 16:35:30.910167: RFID硬件扫描已启动，阅读器开始持续工作
2025-08-26 16:35:30.911163: RFID持续扫描启动完成
2025-08-26 16:35:30.911163: 增强RFID服务初始化完成，持续扫描已启动
2025-08-26 16:35:30.913159: 📋 从数据库读取主从机配置: channel_1
2025-08-26 16:35:30.913159: 📋 配置详情: 主机模式
2025-08-26 16:35:30.913159: 🚀 主机模式：启动持续数据收集，数据将持续进入共享池
2025-08-26 16:35:30.913159: 🎯 关键修复：使用轮询机制确保标签持续被发现
2025-08-26 16:35:30.914152: 🧹 清空RFID缓冲区（保持tagList），确保数据收集正常工作...
2025-08-26 16:35:30.914152: 清空RFID扫描缓冲区...
2025-08-26 16:35:30.914152: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 16:35:30.914152: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 16:35:30.914152: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:30.914152: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:30.914152: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:30.914152: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:30.914152: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:30.915149: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 16:35:30.915149: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 16:35:30.915149: 🚀 启动增强数据收集（事件监听 + 轮询备用）...
2025-08-26 16:35:30.915149: 🚀 开始RFID数据收集...
2025-08-26 16:35:30.915149: 📋 扫描结果和缓存已清空
2025-08-26 16:35:30.915149: 清空RFID扫描缓冲区...
2025-08-26 16:35:30.916146: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 16:35:30.916146: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 16:35:30.916146: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:30.916146: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:30.916146: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:30.916146: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:30.916146: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:30.916146: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 16:35:30.916146: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 16:35:30.917142: 🎯 启动数据监听和轮询机制...
2025-08-26 16:35:30.917142: 🚀 标签轮询机制已启动 (每500ms轮询一次)
2025-08-26 16:35:30.917142: ✅ 标签监听改为仅轮询机制（每500ms轮询tagList）
2025-08-26 16:35:30.917142: 📊 当前HWTagProvider状态:
2025-08-26 16:35:30.917142:   - tagList: 0个标签
2025-08-26 16:35:30.917142:   - type: HWTagType.tagList
2025-08-26 16:35:30.917142: 🎯 标签数据获取已统一为轮询机制
2025-08-26 16:35:30.917142: ✅ RFID数据收集已启动，轮询机制运行中
2025-08-26 16:35:30.918139: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:30.918139: subThread :ReaderCommand.readerList
2025-08-26 16:35:30.918139: commandRsp:ReaderCommand.readerList
2025-08-26 16:35:30.918139: readerList：1,readerSetting：1
2025-08-26 16:35:30.918139: cacheUsedReaders:1
2025-08-26 16:35:30.918139: subThread :ReaderCommand.open
2025-08-26 16:35:30.918139: commandRsp:ReaderCommand.open
2025-08-26 16:35:30.919136: LSGate使用网络连接 IP: **************, Port: 6012, DeviceType: LSGControlCenter
2025-08-26 16:35:30.919136: LSGate device opened successfully, handle: 2424588958144
2025-08-26 16:35:30.919136: open reader readerType ：22 ret：0
2025-08-26 16:35:30.919136: [[22, 0]]
2025-08-26 16:35:30.919136: changeType:ReaderErrorType.openSuccess
2025-08-26 16:35:30.920134: subThread :ReaderCommand.untilDetected
2025-08-26 16:35:30.920134: commandRsp:ReaderCommand.untilDetected
2025-08-26 16:35:30.920134: subThread :ReaderCommand.startInventory
2025-08-26 16:35:30.920134: commandRsp:ReaderCommand.startInventory
2025-08-26 16:35:31.018807: 🔄 执行首次轮询...
2025-08-26 16:35:31.019803: 🔄 开始RFID轮询检查...
2025-08-26 16:35:31.019803: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:31.019803: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:31.418481: 🔄 开始RFID轮询检查...
2025-08-26 16:35:31.419479: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:31.419479: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:31.526124: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:31.527121:   - 设备句柄: 2424588958144
2025-08-26 16:35:31.527121:   - FetchRecords返回值: 0
2025-08-26 16:35:31.528118:   - 报告数量: 0
2025-08-26 16:35:31.528118: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:31.529114:   - 发现标签数量: 0
2025-08-26 16:35:31.529114:   - 未发现任何RFID标签
2025-08-26 16:35:31.529114: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:31.530111: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:31.918821: 🔍 验证数据收集状态...
2025-08-26 16:35:31.919819: 📊 当前tagList: 0个标签
2025-08-26 16:35:31.919819: ⚠️ tagList为空，等待RFID硬件扫描到标签
2025-08-26 16:35:31.920815: ✅ 主机持续数据收集已启动，共享池将持续接收RFID数据
2025-08-26 16:35:31.920815: 🔄 轮询机制每500ms检查一次tagList，确保标签不会丢失
2025-08-26 16:35:31.920815: 开始全局初始化共享扫描池服务...
2025-08-26 16:35:31.920815: 共享扫描池已集成现有RFID服务
2025-08-26 16:35:31.920815: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 16:35:31.921811: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:31.921811: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:31.921811: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:31.921811: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:31.921811: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:31.922808: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:35:31.922808: 🔄 开始RFID轮询检查...
2025-08-26 16:35:31.922808: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:31.922808: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:31.922808: 🔄 开始RFID轮询检查...
2025-08-26 16:35:31.923805: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:31.923805: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:31.923805: 共享扫描池服务全局初始化完成
2025-08-26 16:35:31.923805: 🚀 初始化新架构服务...
2025-08-26 16:35:31.923805: 🖥️ 主机模式：使用主机集合A服务
2025-08-26 16:35:31.923805: ⏹️ 书籍信息查询服务停止监听
2025-08-26 16:35:31.924801: 🚀 书籍信息查询服务开始监听集合A变化
2025-08-26 16:35:31.924801: ✅ 新架构服务初始化完成
2025-08-26 16:35:31.924801: RFID服务和共享池初始化完成，持续扫描已启动
2025-08-26 16:35:31.924801: 闸机协调器初始化完成
2025-08-26 16:35:31.924801: 🔧 开始初始化主从机扩展（使用持久化配置）...
2025-08-26 16:35:31.924801: 开始初始化主从机扩展...
2025-08-26 16:35:31.925798: 从 seasetting 数据库加载主从机配置成功: channel_1
2025-08-26 16:35:31.925798: 配置详情: 主机模式
2025-08-26 16:35:31.925798: 📡 从 SettingProvider 获取串口配置: COM1 @ 115200
2025-08-26 16:35:31.925798: ✅ 通过 SettingProvider 加载串口配置成功
2025-08-26 16:35:31.926795: 启用主从机扩展: channel_1 (主机)
2025-08-26 16:35:31.926795: ✅ 数据变化通知流已创建
2025-08-26 16:35:31.926795: 共享扫描池已集成现有RFID服务
2025-08-26 16:35:31.926795: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 16:35:31.926795: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:31.926795: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:31.926795: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:31.926795: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:31.927791: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:31.927791: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:35:31.927791: 🔄 开始RFID轮询检查...
2025-08-26 16:35:31.927791: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:31.927791: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:31.927791: 配置为主机模式，监听端口: 8888
2025-08-26 16:35:31.928788: 主机服务器启动成功，监听端口: 8888
2025-08-26 16:35:31.928788: 主机模式配置完成（请求-响应模式）
2025-08-26 16:35:31.928788: [channel_1] 已集成现有GateCoordinator，开始监听事件
2025-08-26 16:35:31.929785: 主从机扩展启用成功
2025-08-26 16:35:31.929785: 主从机扩展初始化完成
2025-08-26 16:35:31.929785: 配置信息: MasterSlaveConfig(channelId: channel_1, isMaster: true, slaveAddress: null, masterAddress: null, port: 8888)
2025-08-26 16:35:31.929785: ✅ 加载到持久化配置: 主机模式, 通道: channel_1
2025-08-26 16:35:31.929785: 主从机扩展初始化完成
2025-08-26 16:35:31.929785: 安全闸机系统初始化完成
2025-08-26 16:35:31.929785: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:31.929785:   - 设备句柄: 2424588958144
2025-08-26 16:35:31.930781:   - FetchRecords返回值: 0
2025-08-26 16:35:31.930781:   - 报告数量: 0
2025-08-26 16:35:31.930781: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:31.930781:   - 发现标签数量: 0
2025-08-26 16:35:31.930781:   - 未发现任何RFID标签
2025-08-26 16:35:31.930781: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:31.930781: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:31.939751: 开始初始化MultiAuthManager...
2025-08-26 16:35:31.939751: 多认证管理器状态变更: initializing
2025-08-26 16:35:31.939751: 认证优先级管理器: 开始加载认证方式
2025-08-26 16:35:31.940749: 配置的排序: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 16:35:31.940749: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 16:35:31.940749: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-26 16:35:31.940749: 认证优先级管理器: 最终排序结果: 读者证
2025-08-26 16:35:31.940749: 认证优先级管理器: 主要认证方式: 读者证
2025-08-26 16:35:31.940749: 多认证管理器: 从优先级管理器加载的认证方式: 读者证
2025-08-26 16:35:31.940749: 多认证管理器: 当前默认显示方式: 读者证
2025-08-26 16:35:31.941745: 初始化读卡器认证服务
2025-08-26 16:35:31.941745: 读卡器认证服务初始化成功
2025-08-26 16:35:31.941745: 初始化共享读卡器认证服务
2025-08-26 16:35:31.941745: 读者证 认证服务初始化成功
2025-08-26 16:35:31.941745: 认证服务初始化完成，共初始化 1 种认证方式
2025-08-26 16:35:31.941745: 多认证管理器状态变更: idle
2025-08-26 16:35:31.942741: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.readerCard]
2025-08-26 16:35:31.942741: MultiAuthManager初始化完成
2025-08-26 16:35:31.942741: 开始初始化SilencePageViewModel...
2025-08-26 16:35:31.942741: 闸机串口服务已经初始化
2025-08-26 16:35:31.942741: 开始初始化闸机认证服务...
2025-08-26 16:35:31.942741: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-26 16:35:31.942741: RFID服务已经初始化
2025-08-26 16:35:31.942741: SIP2图书信息服务初始化完成
2025-08-26 16:35:31.942741: 💡 主从机扩展已准备就绪，请通过配置页面手动启用
2025-08-26 16:35:31.943738: 💡 可以通过MasterSlaveConfigPage进行配置
2025-08-26 16:35:31.943738: ✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream
2025-08-26 16:35:31.943738: 串口监听已经启动
2025-08-26 16:35:31.943738: SilencePageViewModel初始化完成
2025-08-26 16:35:32.281141: dispose IndexPage
2025-08-26 16:35:32.281141: IndexPage dispose
2025-08-26 16:35:32.418685: 🔄 开始RFID轮询检查...
2025-08-26 16:35:32.418685: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:32.419682: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:32.419682: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:32.420679:   - 设备句柄: 2424588958144
2025-08-26 16:35:32.420679:   - FetchRecords返回值: 0
2025-08-26 16:35:32.421676:   - 报告数量: 0
2025-08-26 16:35:32.421676: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:32.421676:   - 发现标签数量: 0
2025-08-26 16:35:32.421676:   - 未发现任何RFID标签
2025-08-26 16:35:32.422672: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:32.422672: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:32.444602: 🔍 主从机模式检测: 启用=true, 主机模式=true
2025-08-26 16:35:32.445599: 🔍 扩展详细状态: {enabled: true, channel_id: channel_1, is_master: true, data_stream_ready: true, data_stream_exists: true, data_stream_closed: false, shared_pool_size: 0, queue_size: 0, comm_connected: false, timestamp: 2025-08-26T16:35:32.444602}
2025-08-26 16:35:32.445599: 🎯 检测到主机模式，无需设置数据监听
2025-08-26 16:35:32.918814: 🔄 开始RFID轮询检查...
2025-08-26 16:35:32.920810: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:32.921807: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:32.921807: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:32.922797:   - 设备句柄: 2424588958144
2025-08-26 16:35:32.922797:   - FetchRecords返回值: 0
2025-08-26 16:35:32.922797:   - 报告数量: 0
2025-08-26 16:35:32.923793: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:32.923793:   - 发现标签数量: 0
2025-08-26 16:35:32.923793:   - 未发现任何RFID标签
2025-08-26 16:35:32.923793: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:32.923793: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:33.419150: 🔄 开始RFID轮询检查...
2025-08-26 16:35:33.420153: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:33.420153: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:33.422141: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:33.422141:   - 设备句柄: 2424588958144
2025-08-26 16:35:33.423137:   - FetchRecords返回值: 0
2025-08-26 16:35:33.423137:   - 报告数量: 0
2025-08-26 16:35:33.424134: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:33.424134:   - 发现标签数量: 0
2025-08-26 16:35:33.424134:   - 未发现任何RFID标签
2025-08-26 16:35:33.425129: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:33.425129: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:33.918494: 🔄 开始RFID轮询检查...
2025-08-26 16:35:33.918494: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:33.919494: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:33.919494: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:33.920488:   - 设备句柄: 2424588958144
2025-08-26 16:35:33.920488:   - FetchRecords返回值: 0
2025-08-26 16:35:33.921485:   - 报告数量: 0
2025-08-26 16:35:33.921485: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:33.922484:   - 发现标签数量: 0
2025-08-26 16:35:33.922484:   - 未发现任何RFID标签
2025-08-26 16:35:33.922484: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:33.923481: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:34.418839: 🔄 开始RFID轮询检查...
2025-08-26 16:35:34.419837: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:34.420831: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:34.421826: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:34.421826:   - 设备句柄: 2424588958144
2025-08-26 16:35:34.421826:   - FetchRecords返回值: 0
2025-08-26 16:35:34.422822:   - 报告数量: 0
2025-08-26 16:35:34.422822: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:34.422822:   - 发现标签数量: 0
2025-08-26 16:35:34.423818:   - 未发现任何RFID标签
2025-08-26 16:35:34.423818: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:34.423818: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:34.920173: 🔄 开始RFID轮询检查...
2025-08-26 16:35:34.921170: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:34.921170: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:34.922167: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:34.922167:   - 设备句柄: 2424588958144
2025-08-26 16:35:34.923163:   - FetchRecords返回值: 0
2025-08-26 16:35:34.923163:   - 报告数量: 0
2025-08-26 16:35:34.924160: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:34.924160:   - 发现标签数量: 0
2025-08-26 16:35:34.924160:   - 未发现任何RFID标签
2025-08-26 16:35:34.925156: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:34.925156: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:35.419072: 🔄 开始RFID轮询检查...
2025-08-26 16:35:35.419072: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:35.420069: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:35.420069: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:35.421065:   - 设备句柄: 2424588958144
2025-08-26 16:35:35.421065:   - FetchRecords返回值: 0
2025-08-26 16:35:35.421065:   - 报告数量: 0
2025-08-26 16:35:35.422062: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:35.422062:   - 发现标签数量: 0
2025-08-26 16:35:35.422062:   - 未发现任何RFID标签
2025-08-26 16:35:35.423059: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:35.423059: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:35.918954: 🔄 开始RFID轮询检查...
2025-08-26 16:35:35.918954: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:35.919952: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:35.919952: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:35.919952:   - 设备句柄: 2424588958144
2025-08-26 16:35:35.920948:   - FetchRecords返回值: 0
2025-08-26 16:35:35.920948:   - 报告数量: 0
2025-08-26 16:35:35.920948: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:35.921945:   - 发现标签数量: 0
2025-08-26 16:35:35.921945:   - 未发现任何RFID标签
2025-08-26 16:35:35.921945: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:35.922941: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:36.419295: 🔄 开始RFID轮询检查...
2025-08-26 16:35:36.419295: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:36.420293: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:36.420293: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:36.421289:   - 设备句柄: 2424588958144
2025-08-26 16:35:36.421289:   - FetchRecords返回值: 0
2025-08-26 16:35:36.421289:   - 报告数量: 0
2025-08-26 16:35:36.422285: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:36.422285:   - 发现标签数量: 0
2025-08-26 16:35:36.422285:   - 未发现任何RFID标签
2025-08-26 16:35:36.423282: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:36.423282: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:36.918639: 🔄 开始RFID轮询检查...
2025-08-26 16:35:36.918639: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:36.919637: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:36.919637: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:36.919637:   - 设备句柄: 2424588958144
2025-08-26 16:35:36.920633:   - FetchRecords返回值: 0
2025-08-26 16:35:36.920633:   - 报告数量: 0
2025-08-26 16:35:36.920633: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:36.921629:   - 发现标签数量: 0
2025-08-26 16:35:36.921629:   - 未发现任何RFID标签
2025-08-26 16:35:36.921629: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:36.922626: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:37.418980: 🔄 开始RFID轮询检查...
2025-08-26 16:35:37.418980: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:37.419978: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:37.419978: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:37.420974:   - 设备句柄: 2424588958144
2025-08-26 16:35:37.420974:   - FetchRecords返回值: 0
2025-08-26 16:35:37.420974:   - 报告数量: 0
2025-08-26 16:35:37.421970: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:37.421970:   - 发现标签数量: 0
2025-08-26 16:35:37.421970:   - 未发现任何RFID标签
2025-08-26 16:35:37.422967: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:37.422967: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:37.918324: 🔄 开始RFID轮询检查...
2025-08-26 16:35:37.918324: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:37.919322: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:37.919322: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:37.919322:   - 设备句柄: 2424588958144
2025-08-26 16:35:37.920318:   - FetchRecords返回值: 0
2025-08-26 16:35:37.920318:   - 报告数量: 0
2025-08-26 16:35:37.920318: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:37.921314:   - 发现标签数量: 0
2025-08-26 16:35:37.921314:   - 未发现任何RFID标签
2025-08-26 16:35:37.921314: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:37.922312: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:38.419195: 🔄 开始RFID轮询检查...
2025-08-26 16:35:38.419195: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:38.420193: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:38.420193: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:38.420193:   - 设备句柄: 2424588958144
2025-08-26 16:35:38.421189:   - FetchRecords返回值: 0
2025-08-26 16:35:38.421189:   - 报告数量: 0
2025-08-26 16:35:38.421189: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:38.422185:   - 发现标签数量: 0
2025-08-26 16:35:38.422185:   - 未发现任何RFID标签
2025-08-26 16:35:38.422185: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:38.423182: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:38.918539: 🔄 开始RFID轮询检查...
2025-08-26 16:35:38.918539: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:38.919537: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:38.919537: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:38.920533:   - 设备句柄: 2424588958144
2025-08-26 16:35:38.920533:   - FetchRecords返回值: 0
2025-08-26 16:35:38.920533:   - 报告数量: 0
2025-08-26 16:35:38.921530: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:38.921530:   - 发现标签数量: 0
2025-08-26 16:35:38.921530:   - 未发现任何RFID标签
2025-08-26 16:35:38.922527: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:38.922527: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:39.418880: 🔄 开始RFID轮询检查...
2025-08-26 16:35:39.418880: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:39.419877: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:39.419877: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:39.419877:   - 设备句柄: 2424588958144
2025-08-26 16:35:39.420874:   - FetchRecords返回值: 0
2025-08-26 16:35:39.420874:   - 报告数量: 0
2025-08-26 16:35:39.420874: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:39.421870:   - 发现标签数量: 0
2025-08-26 16:35:39.421870:   - 未发现任何RFID标签
2025-08-26 16:35:39.421870: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:39.422867: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:39.918761: 🔄 开始RFID轮询检查...
2025-08-26 16:35:39.918761: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:39.919759: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:39.919759: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:39.919759:   - 设备句柄: 2424588958144
2025-08-26 16:35:39.920755:   - FetchRecords返回值: 0
2025-08-26 16:35:39.920755:   - 报告数量: 0
2025-08-26 16:35:39.920755: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:39.921755:   - 发现标签数量: 0
2025-08-26 16:35:39.921755:   - 未发现任何RFID标签
2025-08-26 16:35:39.921755: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:39.922748: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:40.419102: 🔄 开始RFID轮询检查...
2025-08-26 16:35:40.419102: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:40.420100: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:40.420100: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:40.421096:   - 设备句柄: 2424588958144
2025-08-26 16:35:40.421096:   - FetchRecords返回值: 0
2025-08-26 16:35:40.421096:   - 报告数量: 0
2025-08-26 16:35:40.422092: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:40.422092:   - 发现标签数量: 0
2025-08-26 16:35:40.422092:   - 未发现任何RFID标签
2025-08-26 16:35:40.423089: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:40.423089: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:40.918446: 🔄 开始RFID轮询检查...
2025-08-26 16:35:40.918446: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:40.919444: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:40.919444: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:40.919444:   - 设备句柄: 2424588958144
2025-08-26 16:35:40.920440:   - FetchRecords返回值: 0
2025-08-26 16:35:40.920440:   - 报告数量: 0
2025-08-26 16:35:40.920440: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:40.921437:   - 发现标签数量: 0
2025-08-26 16:35:40.921437:   - 未发现任何RFID标签
2025-08-26 16:35:40.921437: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:40.922433: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:41.007694: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-26 16:35:41.007694: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-26 16:35:41.008691: 🔍 数据长度: 8 字节
2025-08-26 16:35:41.008691: 🔍 预定义命令列表:
2025-08-26 16:35:41.008691:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:35:41.009687:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:35:41.009687:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:35:41.009687:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:35:41.009687:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:35:41.010684:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:35:41.010684:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:35:41.010684:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:35:41.011681:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:35:41.011681:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:35:41.011681: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-26 16:35:41.011681: 解析到闸机命令: exit_start (出馆开始)
2025-08-26 16:35:41.011681: 收到闸机命令: exit_start (出馆开始)
2025-08-26 16:35:41.012677: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-26 16:35:41.012677: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-26 16:35:41.012677: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-26 16:35:41.012677: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-26 16:35:41.012677: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:35:41.013674: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:35:41.013674: 闸机状态变更: GateState.exitStarted
2025-08-26 16:35:41.013674: 🎨 处理状态变更UI: exitStarted
2025-08-26 16:35:41.013674: 未处理的状态变更UI: exitStarted
2025-08-26 16:35:41.013674: [channel_1] 收到闸机事件: exit_start
2025-08-26 16:35:41.014670: [channel_1] 主从机扩展：处理出馆开始（请求-响应模式）
2025-08-26 16:35:41.014670: 扫描结果已清空
2025-08-26 16:35:41.014670: 🧹 [channel_1] 已清空RFID服务扫描结果（页面计数重置）
2025-08-26 16:35:41.014670: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 16:35:41.014670: 🧹 开始清空共享扫描池...
2025-08-26 16:35:41.014670: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 16:35:41.014670: 🔄 重置RFID去重集合...
2025-08-26 16:35:41.014670: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:41.014670: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:41.014670: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:41.015667: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:41.015667: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:41.015667: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:35:41.015667: 🔄 开始RFID轮询检查...
2025-08-26 16:35:41.015667: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:41.015667: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:41.015667: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 16:35:41.015667: 📡 RFID扫描状态（清空后）: isScanning=false
2025-08-26 16:35:41.015667: ⚠️ RFID未在扫描状态，尝试启动数据收集以恢复轮询...
2025-08-26 16:35:41.015667: 开始RFID数据收集...
2025-08-26 16:35:41.015667: 🔄 RFID数据收集已在进行中，重置防重复机制
2025-08-26 16:35:41.016664: ✅ 已处理条码列表已清空，轮询将重新发现标签
2025-08-26 16:35:41.016664: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 16:35:41.016664: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 16:35:41.016664: 清空RFID扫描缓冲区...
2025-08-26 16:35:41.016664: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 16:35:41.016664: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 16:35:41.016664: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:41.016664: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:41.016664: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:41.017660: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:41.017660: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:41.017660: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:35:41.017660: 🔄 开始RFID轮询检查...
2025-08-26 16:35:41.017660: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:41.017660: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:41.017660: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 16:35:41.017660: 📨 收到GateCoordinator事件: exit_start
2025-08-26 16:35:41.018657: 页面状态变更: SilencePageState.waitingExit
2025-08-26 16:35:41.018657: RFID数据收集已启动
2025-08-26 16:35:41.018657: ✅ 已启动RFID数据收集（恢复轮询）
2025-08-26 16:35:41.018657: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:41.018657: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:41.018657: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:41.018657: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:41.018657: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:41.018657: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:35:41.019654: 🔄 开始RFID轮询检查...
2025-08-26 16:35:41.019654: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:41.019654: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:41.019654: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 16:35:41.019654: 🧹 [channel_1] 主机清空列表1和RFID缓冲区: 清除0个条码
2025-08-26 16:35:41.419328: 🔄 开始RFID轮询检查...
2025-08-26 16:35:41.419328: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:41.419328: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:41.424312: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:41.424312:   - 设备句柄: 2424588958144
2025-08-26 16:35:41.424312:   - FetchRecords返回值: 0
2025-08-26 16:35:41.425309:   - 报告数量: 0
2025-08-26 16:35:41.425309: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:41.425309:   - 发现标签数量: 0
2025-08-26 16:35:41.425309:   - 未发现任何RFID标签
2025-08-26 16:35:41.425309: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:41.425309: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:41.546907: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 16:35:41.546907: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 16:35:41.546907: 🔍 数据长度: 8 字节
2025-08-26 16:35:41.546907: 🔍 预定义命令列表:
2025-08-26 16:35:41.547903:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:35:41.547903:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:35:41.547903:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:35:41.547903:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:35:41.547903:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:35:41.547903:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:35:41.548904:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:35:41.548904:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:35:41.548904:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:35:41.548904:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:35:41.549901: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-26 16:35:41.549901: 解析到闸机命令: position_reached (到达指定位置)
2025-08-26 16:35:41.550897: 收到闸机命令: position_reached (到达指定位置)
2025-08-26 16:35:41.551889: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-26 16:35:41.551889: 📊 流程状态：进馆=false, 出馆=true
2025-08-26 16:35:41.551889: 📊 待处理认证：进馆=false, 出馆=false
2025-08-26 16:35:41.551889: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-26 16:35:41.552886: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 16:35:41.552886: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 16:35:41.552886: 🔐 启动出馆认证系统（不关注结果）...
2025-08-26 16:35:41.552886: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 16:35:41.552886: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 16:35:41.552886: 多认证管理器状态变更: listening
2025-08-26 16:35:41.552886: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-26 16:35:41.552886: 准备启动 1 个物理认证服务
2025-08-26 16:35:41.552886: 开始读卡器认证监听
2025-08-26 16:35:41.553882: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-26 16:35:41.553882: 已移除读卡器状态监听器
2025-08-26 16:35:41.553882: 已移除标签数据监听器
2025-08-26 16:35:41.553882: 所有卡片监听器已移除
2025-08-26 16:35:41.553882: 已添加读卡器状态监听器
2025-08-26 16:35:41.553882: 已添加标签数据监听器
2025-08-26 16:35:41.553882: 开始监听卡片数据 - 所有监听器已就绪
2025-08-26 16:35:41.553882: 读卡器认证监听启动成功
2025-08-26 16:35:41.553882: ✅ 出馆认证系统已启动
2025-08-26 16:35:41.554879: 🚀 启动出馆10秒数据收集...
2025-08-26 16:35:41.554879: 🔧 启动10秒计时器，当前时间: 2025-08-26 16:35:41.550897
2025-08-26 16:35:41.554879: 🔧 10秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-26 16:35:41.554879: 📡 开始从共享池收集数据...
2025-08-26 16:35:41.554879: 🔧 RFID扫描已在运行，只清空共享池准备收集新数据
2025-08-26 16:35:41.554879: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 16:35:41.554879: 🧹 开始清空共享扫描池...
2025-08-26 16:35:41.554879: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 16:35:41.554879: 🔄 重置RFID去重集合...
2025-08-26 16:35:41.554879: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:41.555876: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:41.555876: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:41.555876: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:41.555876: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:41.555876: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:35:41.555876: 🔄 开始RFID轮询检查...
2025-08-26 16:35:41.555876: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:41.555876: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:41.555876: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 16:35:41.555876: 📡 RFID扫描状态（清空后）: isScanning=true
2025-08-26 16:35:41.556873: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 16:35:41.556873: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 16:35:41.556873: 清空RFID扫描缓冲区...
2025-08-26 16:35:41.556873: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 16:35:41.556873: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 16:35:41.556873: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:41.556873: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:41.556873: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:41.556873: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:41.556873: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:41.556873: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:35:41.557869: 🔄 开始RFID轮询检查...
2025-08-26 16:35:41.557869: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:41.557869: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:41.557869: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 16:35:41.557869: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:35:41.557869: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:35:41.557869: 闸机状态变更: GateState.exitWaitingAuth
2025-08-26 16:35:41.557869: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-26 16:35:41.558866: 未处理的状态变更UI: exitWaitingAuth
2025-08-26 16:35:41.558866: 读者证 认证服务启动成功
2025-08-26 16:35:41.558866: 所有认证服务启动完成，成功启动 1 个服务
2025-08-26 16:35:41.558866: 当前可用的认证方式: 读者证
2025-08-26 16:35:41.558866: 🔄 开始重置已处理条码集合...
2025-08-26 16:35:41.558866: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:35:41.558866: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:35:41.558866: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:35:41.559862: 📊 当前tagList状态: 0个标签
2025-08-26 16:35:41.559862: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:35:41.559862: 🔄 开始RFID轮询检查...
2025-08-26 16:35:41.559862: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:41.559862: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:41.559862: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 16:35:41.559862: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:35:41.559862: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:35:41.559862: 闸机状态变更: GateState.exitScanning
2025-08-26 16:35:41.559862: 🎨 处理状态变更UI: exitScanning
2025-08-26 16:35:41.560859: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 16:35:41.560859: 🧪 模拟模式：检测到测试卡片（调用次数: 1）
2025-08-26 16:35:41.560859: 🧪 模拟模式 - 使用测试patron: 2017621493 (索引: 0, 总调用次数: 1)
2025-08-26 16:35:41.560859: 🧪 模拟检测到卡片: 2017621493
2025-08-26 16:35:41.560859: 读卡器数据认证：
2025-08-26 16:35:41.560859:   设备类型: 10
2025-08-26 16:35:41.561856:   条码: 2017621493
2025-08-26 16:35:41.561856:   标签UID: SIM2017621493
2025-08-26 16:35:41.561856:   对应登录类型: AuthLoginType.readerCard
2025-08-26 16:35:41.561856:   根据读卡器类型10确定认证方式为: 读者证
2025-08-26 16:35:41.561856:   开始调用认证API: 2017621493
2025-08-26 16:35:41.561856: 正在认证用户: 2017621493, 方式: 读者证
2025-08-26 16:35:41.561856: 多认证管理器: 读者证获得认证请求锁
2025-08-26 16:35:41.561856: 使用门径认证接口: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 16:35:41.561856: 开始门径认证: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 16:35:41.561856: 发送认证请求: {deviceMac: FFFFFFFF, patronSn: 2017621493, cardSn: null, type: 2}
2025-08-26 16:35:41.562852: 设备API服务初始化完成: http://166.111.120.166:9000
2025-08-26 16:35:41.562852: 发送读者认证请求: /tunano/ldc/entrance/v1/api/door/verify
2025-08-26 16:35:41.562852: 请求数据: {"deviceMac":"FFFFFFFF","patronSn":"2017621493","cardSn":null,"type":2}
2025-08-26 16:35:41.584782: 读者认证响应: {errorCode: 0, message: 验证通过, data: null}
2025-08-26 16:35:41.585777: 认证响应: errorCode=0, message=验证通过
2025-08-26 16:35:41.585777: 认证成功，跳过用户信息获取
2025-08-26 16:35:41.585777: 门径认证结果: AuthStatus.success, 用户=认证用户
2025-08-26 16:35:41.585777:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-26 16:35:41.585777: 认证结果已产生，立即停止读卡器扫描
2025-08-26 16:35:41.585777: 没有活跃的读卡器连接需要停止
2025-08-26 16:35:41.586773: 多认证管理器: 收到认证结果，立即停止所有读卡器扫描
2025-08-26 16:35:41.586773: 立即停止所有读卡器扫描
2025-08-26 16:35:41.586773: 停止读卡器认证监听
2025-08-26 16:35:41.586773: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-26 16:35:41.586773: 多认证管理器状态变更: authenticating
2025-08-26 16:35:41.586773: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-26 16:35:41.586773: 多认证管理器状态变更: completed
2025-08-26 16:35:41.586773: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-26 16:35:41.586773: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-26 16:35:41.587770: 已移除读卡器状态监听器
2025-08-26 16:35:41.587770: 已移除标签数据监听器
2025-08-26 16:35:41.587770: 所有卡片监听器已移除
2025-08-26 16:35:41.587770: 没有活跃的读卡器连接需要暂停
2025-08-26 16:35:41.587770: 📱 收到出馆认证结果: AuthStatus.success, 用户: 认证用户
2025-08-26 16:35:41.587770: ✅ 出馆认证API请求已发送，继续数据收集流程（不关注认证结果）
2025-08-26 16:35:41.587770: 读卡器认证监听已停止（连接保持）
2025-08-26 16:35:41.587770: 已停止 读者证 扫描
2025-08-26 16:35:41.587770: [channel_1] 收到闸机事件: exit_auth_success
2025-08-26 16:35:41.588766: 📨 收到GateCoordinator事件: exit_auth_success
2025-08-26 16:35:41.588766: 未处理的GateCoordinator事件: exit_auth_success
2025-08-26 16:35:41.919669: 🔄 开始RFID轮询检查...
2025-08-26 16:35:41.919669: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:41.919669: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:41.920667: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:41.920667:   - 设备句柄: 2424588958144
2025-08-26 16:35:41.920667:   - FetchRecords返回值: 0
2025-08-26 16:35:41.920667:   - 报告数量: 0
2025-08-26 16:35:41.921663: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:41.921663:   - 发现标签数量: 0
2025-08-26 16:35:41.921663:   - 未发现任何RFID标签
2025-08-26 16:35:41.921663: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:35:41.921663: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:35:42.020335: 📊 [channel_1] 主机返回当前数据: 0个条码
2025-08-26 16:35:42.419016: 🔄 开始RFID轮询检查...
2025-08-26 16:35:42.420011: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:35:42.420011: ⚠️ tagList为空，场上无标签
2025-08-26 16:35:42.420011: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:42.421008:   - 设备句柄: 2424588958144
2025-08-26 16:35:42.421008:   - FetchRecords返回值: 0
2025-08-26 16:35:42.421008:   - 报告数量: 1
2025-08-26 16:35:42.422004:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:42.422004:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:42.422004:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:42.422004:   - 设备类型: LSGControlCenter
2025-08-26 16:35:42.422004:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:42.422004:   - 数据长度: 8
2025-08-26 16:35:42.422004:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:42.423:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:42.423: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:42.423:   - 发现标签数量: 1
2025-08-26 16:35:42.423:   - 标签详情:
2025-08-26 16:35:42.423:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:42.423: RFID扫描: 发现 1 个标签
2025-08-26 16:35:42.423997: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 16:35:42.423997: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:42.423997: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:42.423997:   - UID: E004015304F3DD22
2025-08-26 16:35:42.423997:   - Data: E004015304F3DD22
2025-08-26 16:35:42.423997:   - EventType: 1
2025-08-26 16:35:42.423997:   - Direction: 0
2025-08-26 16:35:42.424994:   - Antenna: 1
2025-08-26 16:35:42.424994:   - TagFrequency: 0
2025-08-26 16:35:42.424994: data:E004015304F3DD22,coder:Coder15962Std
2025-08-26 16:35:42.424994: parseRet：{"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]},decoder:15962标准协议
2025-08-26 16:35:42.424994: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-26 16:35:42.424994: 🏷️ 新标签[0]: uid=E004015304F3DD22, barCode=null, readerType=22
2025-08-26 16:35:42.425990: 📡 LSGate标签详情: {uid: E004015304F3DD22, barCode: null, eas: null, tagType: null, libraryCode: null, afiStr: null, afiData: null, readerType: 22, decoderType: 15962标准协议, dsfID: null, oidList: [{oid: 0, compressMode: 110, data: S, originHexStr: E004015300000000, originData: [53], isKeepInTag: true}], data: E004015304F3DD22, leftBinary: null, sortType: null, codeType: null, version: null, contentIndex: null, tagFrequency: 0, info: null, inAnt: 1}
2025-08-26 16:35:42.425990: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-26 16:35:42.919354: 🔄 开始RFID轮询检查...
2025-08-26 16:35:42.920352: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 16:35:42.920352: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:42.920352: 🆕 轮询发现新标签(UID作为条码): E004015304F3DD22
2025-08-26 16:35:42.920352: 📋 添加到已处理列表: 0 -> 1
2025-08-26 16:35:42.920352: 🏷️ 检测到标签: E004015304F3DD22
2025-08-26 16:35:42.920352: 📊 当前扫描状态: 扫描中=true, 已扫描=1个
2025-08-26 16:35:42.920352: 📋 已扫描列表: [E004015304F3DD22]
2025-08-26 16:35:42.921349: ✅ 条码已发送到barcodeStream，将进入共享池: E004015304F3DD22
2025-08-26 16:35:42.921349: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 16:35:42.921349: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 16:35:42.921349: 📋 当前已处理标签列表: [E004015304F3DD22]
2025-08-26 16:35:42.921349: 🔄 尝试添加条码到共享池: E004015304F3DD22
2025-08-26 16:35:42.921349: 📊 添加前状态: 共享池大小=0, 是否为空=true
2025-08-26 16:35:42.922344: ✅ 成功添加条码到共享池: E004015304F3DD22 (总计: 1)
2025-08-26 16:35:42.922344: 📋 当前共享池内容: [E004015304F3DD22]
2025-08-26 16:35:42.922344: 📡 共享池变化通知已发送
2025-08-26 16:35:42.922344: 🔄 尝试添加条码到共享池: E004015304F3DD22
2025-08-26 16:35:42.922344: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 16:35:42.922344: 🔄 条码已存在于共享池: E004015304F3DD22 (总计: 1)
2025-08-26 16:35:42.922344: 扫描到新条码: E004015304F3DD22 (总计: 1)
2025-08-26 16:35:42.922344: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 16:35:42.923341: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:42.923341:   - 设备句柄: 2424588958144
2025-08-26 16:35:42.923341:   - FetchRecords返回值: 0
2025-08-26 16:35:42.923341:   - 报告数量: 2
2025-08-26 16:35:42.923341:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:42.923341:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:42.923341:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:42.923341:   - 设备类型: LSGControlCenter
2025-08-26 16:35:42.924337:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:42.924337:   - 数据长度: 8
2025-08-26 16:35:42.924337:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:42.924337:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:42.924337:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:42.924337:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:42.924337:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:42.925334:   - 设备类型: LSGControlCenter
2025-08-26 16:35:42.925334:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:42.925334:   - 数据长度: 8
2025-08-26 16:35:42.925334:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:42.925334:   - 提取的UID: E004015305F68508
2025-08-26 16:35:42.925334: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:42.925334:   - 发现标签数量: 2
2025-08-26 16:35:42.925334:   - 标签详情:
2025-08-26 16:35:42.926331:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:42.926331:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:42.926331: RFID扫描: 发现 2 个标签
2025-08-26 16:35:42.926331: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 16:35:42.926331: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:42.926331: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:42.926331:   - UID: E004015304F3DD22
2025-08-26 16:35:42.927330:   - Data: E004015304F3DD22
2025-08-26 16:35:42.927330:   - EventType: 1
2025-08-26 16:35:42.927330:   - Direction: 0
2025-08-26 16:35:42.927330:   - Antenna: 1
2025-08-26 16:35:42.928325:   - TagFrequency: 0
2025-08-26 16:35:42.928325: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:42.928325:   - UID: E004015305F68508
2025-08-26 16:35:42.929322:   - Data: E004015305F68508
2025-08-26 16:35:42.929322:   - EventType: 1
2025-08-26 16:35:42.929322:   - Direction: 0
2025-08-26 16:35:42.929322:   - Antenna: 1
2025-08-26 16:35:42.929322:   - TagFrequency: 0
2025-08-26 16:35:42.930318: data:E004015305F68508,coder:Coder15962Std
2025-08-26 16:35:42.930318: parseRet：{"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]},decoder:15962标准协议
2025-08-26 16:35:42.930318: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-26 16:35:42.930318: 🏷️ 新标签[0]: uid=E004015305F68508, barCode=null, readerType=22
2025-08-26 16:35:42.930318: 📡 LSGate标签详情: {uid: E004015305F68508, barCode: null, eas: null, tagType: null, libraryCode: null, afiStr: null, afiData: null, readerType: 22, decoderType: 15962标准协议, dsfID: null, oidList: [{oid: 0, compressMode: 110, data: S, originHexStr: E004015300000000, originData: [53], isKeepInTag: true}], data: E004015305F68508, leftBinary: null, sortType: null, codeType: null, version: null, contentIndex: null, tagFrequency: 0, info: null, inAnt: 1}
2025-08-26 16:35:42.930318: 🎯 HWTagProvider: 当前总标签数 2, 新增标签数 1
2025-08-26 16:35:43.418698: 🔄 开始RFID轮询检查...
2025-08-26 16:35:43.418698: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=1个
2025-08-26 16:35:43.418698: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:43.418698: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:43.419695: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:43.419695: 🆕 轮询发现新标签(UID作为条码): E004015305F68508
2025-08-26 16:35:43.419695: 📋 添加到已处理列表: 1 -> 2
2025-08-26 16:35:43.419695: 🏷️ 检测到标签: E004015305F68508
2025-08-26 16:35:43.419695: 📊 当前扫描状态: 扫描中=true, 已扫描=2个
2025-08-26 16:35:43.419695: 📋 已扫描列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:35:43.419695: ✅ 条码已发送到barcodeStream，将进入共享池: E004015305F68508
2025-08-26 16:35:43.419695: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 16:35:43.419695: ✅ 轮询完成: 发现1个新标签，总计已处理2个标签
2025-08-26 16:35:43.419695: 📋 当前已处理标签列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:35:43.420692: 🔄 尝试添加条码到共享池: E004015305F68508
2025-08-26 16:35:43.420692: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 16:35:43.421691: ✅ 成功添加条码到共享池: E004015305F68508 (总计: 2)
2025-08-26 16:35:43.421691: 📋 当前共享池内容: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:35:43.422686: 📡 共享池变化通知已发送
2025-08-26 16:35:43.422686: 🔄 尝试添加条码到共享池: E004015305F68508
2025-08-26 16:35:43.422686: 📊 添加前状态: 共享池大小=2, 是否为空=false
2025-08-26 16:35:43.422686: 🔄 条码已存在于共享池: E004015305F68508 (总计: 2)
2025-08-26 16:35:43.422686: 扫描到新条码: E004015305F68508 (总计: 2)
2025-08-26 16:35:43.422686: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 16:35:43.423684: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:43.423684:   - 设备句柄: 2424588958144
2025-08-26 16:35:43.423684:   - FetchRecords返回值: 0
2025-08-26 16:35:43.423684:   - 报告数量: 2
2025-08-26 16:35:43.423684:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:43.424679:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:43.424679:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:43.424679:   - 设备类型: LSGControlCenter
2025-08-26 16:35:43.424679:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:43.424679:   - 数据长度: 8
2025-08-26 16:35:43.424679:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:43.424679:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:43.425675:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:43.425675:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:43.425675:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:43.425675:   - 设备类型: LSGControlCenter
2025-08-26 16:35:43.425675:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:43.425675:   - 数据长度: 8
2025-08-26 16:35:43.425675:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:43.425675:   - 提取的UID: E004015305F68508
2025-08-26 16:35:43.426672: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:43.426672:   - 发现标签数量: 2
2025-08-26 16:35:43.426672:   - 标签详情:
2025-08-26 16:35:43.426672:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:43.426672:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:43.426672: RFID扫描: 发现 2 个标签
2025-08-26 16:35:43.426672: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 16:35:43.426672: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:43.427669: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:43.427669:   - UID: E004015304F3DD22
2025-08-26 16:35:43.427669:   - Data: E004015304F3DD22
2025-08-26 16:35:43.427669:   - EventType: 1
2025-08-26 16:35:43.427669:   - Direction: 0
2025-08-26 16:35:43.427669:   - Antenna: 1
2025-08-26 16:35:43.427669:   - TagFrequency: 0
2025-08-26 16:35:43.427669: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:43.428665:   - UID: E004015305F68508
2025-08-26 16:35:43.428665:   - Data: E004015305F68508
2025-08-26 16:35:43.428665:   - EventType: 1
2025-08-26 16:35:43.428665:   - Direction: 0
2025-08-26 16:35:43.428665:   - Antenna: 1
2025-08-26 16:35:43.428665:   - TagFrequency: 0
2025-08-26 16:35:43.919040: 🔄 开始RFID轮询检查...
2025-08-26 16:35:43.919040: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:43.920037: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:43.920037: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:43.920037: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:43.920037: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:43.920037: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:43.921033: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:43.922030:   - 设备句柄: 2424588958144
2025-08-26 16:35:43.922030:   - FetchRecords返回值: 0
2025-08-26 16:35:43.922030:   - 报告数量: 3
2025-08-26 16:35:43.922030:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:43.922030:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:43.923027:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:43.923027:   - 设备类型: LSGControlCenter
2025-08-26 16:35:43.923027:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:43.923027:   - 数据长度: 8
2025-08-26 16:35:43.923027:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:43.923027:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:43.923027:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:43.924023:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:43.924023:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:43.924023:   - 设备类型: LSGControlCenter
2025-08-26 16:35:43.924023:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:43.924023:   - 数据长度: 8
2025-08-26 16:35:43.924023:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:43.925020:   - 提取的UID: E004015305F68508
2025-08-26 16:35:43.925020:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:43.925020:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:43.925020:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:43.925020:   - 设备类型: LSGControlCenter
2025-08-26 16:35:43.925020:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:43.925020:   - 数据长度: 8
2025-08-26 16:35:43.925020:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:43.926016:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:43.926016: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:43.926016:   - 发现标签数量: 3
2025-08-26 16:35:43.926016:   - 标签详情:
2025-08-26 16:35:43.926016:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:43.926016:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:43.926016:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:43.926016: RFID扫描: 发现 3 个标签
2025-08-26 16:35:43.927013: 🔍 LSGate ReaderManager收到扫描结果: 3 个UID
2025-08-26 16:35:43.927013: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:43.927013: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:43.927013:   - UID: E004015304F3DD22
2025-08-26 16:35:43.927013:   - Data: E004015304F3DD22
2025-08-26 16:35:43.927013:   - EventType: 1
2025-08-26 16:35:43.927013:   - Direction: 0
2025-08-26 16:35:43.927013:   - Antenna: 1
2025-08-26 16:35:43.928009:   - TagFrequency: 0
2025-08-26 16:35:43.928009: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:43.928009:   - UID: E004015305F68508
2025-08-26 16:35:43.928009:   - Data: E004015305F68508
2025-08-26 16:35:43.928009:   - EventType: 1
2025-08-26 16:35:43.928009:   - Direction: 0
2025-08-26 16:35:43.928009:   - Antenna: 1
2025-08-26 16:35:43.929006:   - TagFrequency: 0
2025-08-26 16:35:43.929006: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:43.929006:   - UID: E004015304F3DD22
2025-08-26 16:35:43.929006:   - Data: E004015304F3DD22
2025-08-26 16:35:43.929006:   - EventType: 1
2025-08-26 16:35:43.929006:   - Direction: 0
2025-08-26 16:35:43.929006:   - Antenna: 1
2025-08-26 16:35:43.929006:   - TagFrequency: 0
2025-08-26 16:35:44.419902: 🔄 开始RFID轮询检查...
2025-08-26 16:35:44.419902: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:44.419902: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:44.419902: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:44.419902: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:44.420899: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:44.420899: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:44.422892: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:44.422892:   - 设备句柄: 2424588958144
2025-08-26 16:35:44.422892:   - FetchRecords返回值: 0
2025-08-26 16:35:44.422892:   - 报告数量: 4
2025-08-26 16:35:44.422892:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:44.423888:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:44.423888:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:44.423888:   - 设备类型: LSGControlCenter
2025-08-26 16:35:44.423888:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:44.423888:   - 数据长度: 8
2025-08-26 16:35:44.423888:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:44.423888:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:44.424885:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:44.424885:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:44.424885:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:44.424885:   - 设备类型: LSGControlCenter
2025-08-26 16:35:44.425886:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:44.425886:   - 数据长度: 8
2025-08-26 16:35:44.425886:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:44.426879:   - 提取的UID: E004015305F68508
2025-08-26 16:35:44.426879:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:44.426879:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:44.426879:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:44.426879:   - 设备类型: LSGControlCenter
2025-08-26 16:35:44.426879:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:44.427881:   - 数据长度: 8
2025-08-26 16:35:44.427881:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:44.427881:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:44.427881:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:44.427881:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:44.427881:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:44.428872:   - 设备类型: LSGControlCenter
2025-08-26 16:35:44.428872:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:44.428872:   - 数据长度: 8
2025-08-26 16:35:44.428872:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:44.428872:   - 提取的UID: E004015305F68508
2025-08-26 16:35:44.428872: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:44.428872:   - 发现标签数量: 4
2025-08-26 16:35:44.428872:   - 标签详情:
2025-08-26 16:35:44.429868:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:44.429868:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:44.429868:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:44.429868:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:44.429868: RFID扫描: 发现 4 个标签
2025-08-26 16:35:44.429868: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 16:35:44.429868: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:44.430865: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:44.430865:   - UID: E004015304F3DD22
2025-08-26 16:35:44.430865:   - Data: E004015304F3DD22
2025-08-26 16:35:44.430865:   - EventType: 1
2025-08-26 16:35:44.430865:   - Direction: 0
2025-08-26 16:35:44.430865:   - Antenna: 1
2025-08-26 16:35:44.430865:   - TagFrequency: 0
2025-08-26 16:35:44.430865: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:44.431862:   - UID: E004015305F68508
2025-08-26 16:35:44.431862:   - Data: E004015305F68508
2025-08-26 16:35:44.431862:   - EventType: 1
2025-08-26 16:35:44.431862:   - Direction: 0
2025-08-26 16:35:44.431862:   - Antenna: 1
2025-08-26 16:35:44.431862:   - TagFrequency: 0
2025-08-26 16:35:44.431862: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:44.431862:   - UID: E004015304F3DD22
2025-08-26 16:35:44.432859:   - Data: E004015304F3DD22
2025-08-26 16:35:44.432859:   - EventType: 1
2025-08-26 16:35:44.432859:   - Direction: 0
2025-08-26 16:35:44.432859:   - Antenna: 1
2025-08-26 16:35:44.432859:   - TagFrequency: 0
2025-08-26 16:35:44.432859: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:44.432859:   - UID: E004015305F68508
2025-08-26 16:35:44.432859:   - Data: E004015305F68508
2025-08-26 16:35:44.433855:   - EventType: 1
2025-08-26 16:35:44.433855:   - Direction: 0
2025-08-26 16:35:44.433855:   - Antenna: 1
2025-08-26 16:35:44.433855:   - TagFrequency: 0
2025-08-26 16:35:44.919254: 🔄 开始RFID轮询检查...
2025-08-26 16:35:44.920250: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:44.920250: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:44.920250: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:44.921240: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:44.921240: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:44.921240: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:44.922236: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:44.922236:   - 设备句柄: 2424588958144
2025-08-26 16:35:44.923233:   - FetchRecords返回值: 0
2025-08-26 16:35:44.923233:   - 报告数量: 4
2025-08-26 16:35:44.923233:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:44.923233:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:44.923233:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:44.923233:   - 设备类型: LSGControlCenter
2025-08-26 16:35:44.923233:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:44.924229:   - 数据长度: 8
2025-08-26 16:35:44.924229:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:44.924229:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:44.924229:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:44.924229:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:44.924229:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:44.924229:   - 设备类型: LSGControlCenter
2025-08-26 16:35:44.924229:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:44.925226:   - 数据长度: 8
2025-08-26 16:35:44.925226:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:44.925226:   - 提取的UID: E004015305F68508
2025-08-26 16:35:44.925226:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:44.925226:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:44.925226:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:44.925226:   - 设备类型: LSGControlCenter
2025-08-26 16:35:44.925226:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:44.926222:   - 数据长度: 8
2025-08-26 16:35:44.926222:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:44.926222:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:44.926222:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:44.926222:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:44.926222:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:44.926222:   - 设备类型: LSGControlCenter
2025-08-26 16:35:44.926222:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:44.927219:   - 数据长度: 8
2025-08-26 16:35:44.927219:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:44.927219:   - 提取的UID: E004015305F68508
2025-08-26 16:35:44.927219: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:44.927219:   - 发现标签数量: 4
2025-08-26 16:35:44.927219:   - 标签详情:
2025-08-26 16:35:44.927219:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:44.927219:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:44.928216:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:44.928216:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:44.928216: RFID扫描: 发现 4 个标签
2025-08-26 16:35:44.928216: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 16:35:44.928216: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:44.928216: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:44.928216:   - UID: E004015304F3DD22
2025-08-26 16:35:44.928216:   - Data: E004015304F3DD22
2025-08-26 16:35:44.929212:   - EventType: 1
2025-08-26 16:35:44.929212:   - Direction: 0
2025-08-26 16:35:44.929212:   - Antenna: 1
2025-08-26 16:35:44.929212:   - TagFrequency: 0
2025-08-26 16:35:44.929212: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:44.929212:   - UID: E004015305F68508
2025-08-26 16:35:44.929212:   - Data: E004015305F68508
2025-08-26 16:35:44.929212:   - EventType: 1
2025-08-26 16:35:44.930209:   - Direction: 0
2025-08-26 16:35:44.930209:   - Antenna: 1
2025-08-26 16:35:44.930209:   - TagFrequency: 0
2025-08-26 16:35:44.930209: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:44.930209:   - UID: E004015304F3DD22
2025-08-26 16:35:44.930209:   - Data: E004015304F3DD22
2025-08-26 16:35:44.930209:   - EventType: 1
2025-08-26 16:35:44.930209:   - Direction: 0
2025-08-26 16:35:44.931206:   - Antenna: 1
2025-08-26 16:35:44.931206:   - TagFrequency: 0
2025-08-26 16:35:44.931206: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:44.931206:   - UID: E004015305F68508
2025-08-26 16:35:44.931206:   - Data: E004015305F68508
2025-08-26 16:35:44.931206:   - EventType: 1
2025-08-26 16:35:44.931206:   - Direction: 0
2025-08-26 16:35:44.931206:   - Antenna: 1
2025-08-26 16:35:44.932203:   - TagFrequency: 0
2025-08-26 16:35:45.418590: 🔄 开始RFID轮询检查...
2025-08-26 16:35:45.418590: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:45.418590: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:45.418590: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:45.419587: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:45.419587: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:45.419587: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:45.422577: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:45.422577:   - 设备句柄: 2424588958144
2025-08-26 16:35:45.422577:   - FetchRecords返回值: 0
2025-08-26 16:35:45.422577:   - 报告数量: 5
2025-08-26 16:35:45.423574:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.423574:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:45.423574:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.423574:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.423574:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.423574:   - 数据长度: 8
2025-08-26 16:35:45.424570:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:45.424570:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:45.424570:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.424570:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:45.424570:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.424570:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.424570:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.424570:   - 数据长度: 8
2025-08-26 16:35:45.425567:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:45.425567:   - 提取的UID: E004015305F68508
2025-08-26 16:35:45.425567:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.425567:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:45.425567:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.425567:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.425567:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.425567:   - 数据长度: 8
2025-08-26 16:35:45.426563:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:45.426563:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:45.426563:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.426563:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:45.426563:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.426563:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.426563:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.427560:   - 数据长度: 8
2025-08-26 16:35:45.427560:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:45.427560:   - 提取的UID: E004015305F68508
2025-08-26 16:35:45.427560:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.427560:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:45.427560:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.427560:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.427560:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.428557:   - 数据长度: 8
2025-08-26 16:35:45.428557:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:45.428557:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:45.428557: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:45.428557:   - 发现标签数量: 5
2025-08-26 16:35:45.428557:   - 标签详情:
2025-08-26 16:35:45.429557:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:45.429557:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:45.429557:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:45.429557:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:45.429557:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:45.430554: RFID扫描: 发现 5 个标签
2025-08-26 16:35:45.430554: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:45.430554: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:45.430554: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.431548:   - UID: E004015304F3DD22
2025-08-26 16:35:45.431548:   - Data: E004015304F3DD22
2025-08-26 16:35:45.431548:   - EventType: 1
2025-08-26 16:35:45.431548:   - Direction: 0
2025-08-26 16:35:45.431548:   - Antenna: 1
2025-08-26 16:35:45.432544:   - TagFrequency: 0
2025-08-26 16:35:45.432544: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.432544:   - UID: E004015305F68508
2025-08-26 16:35:45.432544:   - Data: E004015305F68508
2025-08-26 16:35:45.432544:   - EventType: 1
2025-08-26 16:35:45.432544:   - Direction: 0
2025-08-26 16:35:45.432544:   - Antenna: 1
2025-08-26 16:35:45.433540:   - TagFrequency: 0
2025-08-26 16:35:45.433540: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.433540:   - UID: E004015304F3DD22
2025-08-26 16:35:45.433540:   - Data: E004015304F3DD22
2025-08-26 16:35:45.433540:   - EventType: 1
2025-08-26 16:35:45.433540:   - Direction: 0
2025-08-26 16:35:45.433540:   - Antenna: 1
2025-08-26 16:35:45.434537:   - TagFrequency: 0
2025-08-26 16:35:45.434537: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.434537:   - UID: E004015305F68508
2025-08-26 16:35:45.434537:   - Data: E004015305F68508
2025-08-26 16:35:45.434537:   - EventType: 1
2025-08-26 16:35:45.434537:   - Direction: 0
2025-08-26 16:35:45.434537:   - Antenna: 1
2025-08-26 16:35:45.434537:   - TagFrequency: 0
2025-08-26 16:35:45.435534: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.435534:   - UID: E004015304F3DD22
2025-08-26 16:35:45.435534:   - Data: E004015304F3DD22
2025-08-26 16:35:45.435534:   - EventType: 1
2025-08-26 16:35:45.435534:   - Direction: 0
2025-08-26 16:35:45.435534:   - Antenna: 1
2025-08-26 16:35:45.435534:   - TagFrequency: 0
2025-08-26 16:35:45.918979: 🔄 开始RFID轮询检查...
2025-08-26 16:35:45.918979: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:45.918979: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:45.918979: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:45.918979: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:45.919976: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:45.919976: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:45.921969: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:45.922972:   - 设备句柄: 2424588958144
2025-08-26 16:35:45.922972:   - FetchRecords返回值: 0
2025-08-26 16:35:45.923964:   - 报告数量: 5
2025-08-26 16:35:45.923964:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.923964:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:45.923964:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.924960:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.924960:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.924960:   - 数据长度: 8
2025-08-26 16:35:45.924960:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:45.924960:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:45.925958:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.925958:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:45.925958:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.925958:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.925958:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.925958:   - 数据长度: 8
2025-08-26 16:35:45.926953:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:45.926953:   - 提取的UID: E004015305F68508
2025-08-26 16:35:45.926953:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.926953:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:45.926953:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.926953:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.926953:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.927950:   - 数据长度: 8
2025-08-26 16:35:45.927950:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:45.927950:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:45.927950:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.927950:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:45.927950:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.927950:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.927950:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.928946:   - 数据长度: 8
2025-08-26 16:35:45.928946:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:45.928946:   - 提取的UID: E004015305F68508
2025-08-26 16:35:45.928946:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:45.928946:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:45.928946:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:45.928946:   - 设备类型: LSGControlCenter
2025-08-26 16:35:45.928946:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:45.929943:   - 数据长度: 8
2025-08-26 16:35:45.929943:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:45.929943:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:45.929943: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:45.929943:   - 发现标签数量: 5
2025-08-26 16:35:45.929943:   - 标签详情:
2025-08-26 16:35:45.929943:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:45.929943:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:45.930939:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:45.930939:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:45.930939:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:45.930939: RFID扫描: 发现 5 个标签
2025-08-26 16:35:45.930939: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:45.930939: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:45.930939: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.930939:   - UID: E004015304F3DD22
2025-08-26 16:35:45.931936:   - Data: E004015304F3DD22
2025-08-26 16:35:45.931936:   - EventType: 1
2025-08-26 16:35:45.931936:   - Direction: 0
2025-08-26 16:35:45.931936:   - Antenna: 1
2025-08-26 16:35:45.931936:   - TagFrequency: 0
2025-08-26 16:35:45.931936: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.931936:   - UID: E004015305F68508
2025-08-26 16:35:45.931936:   - Data: E004015305F68508
2025-08-26 16:35:45.932933:   - EventType: 1
2025-08-26 16:35:45.932933:   - Direction: 0
2025-08-26 16:35:45.932933:   - Antenna: 1
2025-08-26 16:35:45.932933:   - TagFrequency: 0
2025-08-26 16:35:45.932933: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.932933:   - UID: E004015304F3DD22
2025-08-26 16:35:45.932933:   - Data: E004015304F3DD22
2025-08-26 16:35:45.932933:   - EventType: 1
2025-08-26 16:35:45.933929:   - Direction: 0
2025-08-26 16:35:45.933929:   - Antenna: 1
2025-08-26 16:35:45.933929:   - TagFrequency: 0
2025-08-26 16:35:45.933929: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.933929:   - UID: E004015305F68508
2025-08-26 16:35:45.933929:   - Data: E004015305F68508
2025-08-26 16:35:45.933929:   - EventType: 1
2025-08-26 16:35:45.933929:   - Direction: 0
2025-08-26 16:35:45.934926:   - Antenna: 1
2025-08-26 16:35:45.934926:   - TagFrequency: 0
2025-08-26 16:35:45.934926: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:45.934926:   - UID: E004015304F3DD22
2025-08-26 16:35:45.934926:   - Data: E004015304F3DD22
2025-08-26 16:35:45.934926:   - EventType: 1
2025-08-26 16:35:45.934926:   - Direction: 0
2025-08-26 16:35:45.934926:   - Antenna: 1
2025-08-26 16:35:45.935923:   - TagFrequency: 0
2025-08-26 16:35:46.419321: 🔄 开始RFID轮询检查...
2025-08-26 16:35:46.419321: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:46.419321: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:46.420317: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:46.420317: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:46.420317: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:46.420317: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:46.422310: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:46.423307:   - 设备句柄: 2424588958144
2025-08-26 16:35:46.423307:   - FetchRecords返回值: 0
2025-08-26 16:35:46.423307:   - 报告数量: 5
2025-08-26 16:35:46.423307:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.423307:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:46.423307:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.424304:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.424304:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.424304:   - 数据长度: 8
2025-08-26 16:35:46.424304:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:46.424304:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:46.424304:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.424304:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:46.424304:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.425300:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.425300:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.425300:   - 数据长度: 8
2025-08-26 16:35:46.425300:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:46.425300:   - 提取的UID: E004015305F68508
2025-08-26 16:35:46.425300:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.425300:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:46.425300:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.426297:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.426297:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.426297:   - 数据长度: 8
2025-08-26 16:35:46.426297:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:46.426297:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:46.426297:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.426297:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:46.427293:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.427293:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.427293:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.427293:   - 数据长度: 8
2025-08-26 16:35:46.427293:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:46.427293:   - 提取的UID: E004015305F68508
2025-08-26 16:35:46.427293:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.427293:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:46.427293:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.428290:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.428290:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.428290:   - 数据长度: 8
2025-08-26 16:35:46.428290:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:46.428290:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:46.428290: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:46.428290:   - 发现标签数量: 5
2025-08-26 16:35:46.428290:   - 标签详情:
2025-08-26 16:35:46.429287:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:46.429287:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:46.429287:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:46.429287:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:46.429287:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:46.429287: RFID扫描: 发现 5 个标签
2025-08-26 16:35:46.429287: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:46.429287: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:46.430284: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.430284:   - UID: E004015304F3DD22
2025-08-26 16:35:46.430284:   - Data: E004015304F3DD22
2025-08-26 16:35:46.430284:   - EventType: 1
2025-08-26 16:35:46.430284:   - Direction: 0
2025-08-26 16:35:46.430284:   - Antenna: 1
2025-08-26 16:35:46.430284:   - TagFrequency: 0
2025-08-26 16:35:46.430284: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.431280:   - UID: E004015305F68508
2025-08-26 16:35:46.431280:   - Data: E004015305F68508
2025-08-26 16:35:46.431280:   - EventType: 1
2025-08-26 16:35:46.431280:   - Direction: 0
2025-08-26 16:35:46.431280:   - Antenna: 1
2025-08-26 16:35:46.431280:   - TagFrequency: 0
2025-08-26 16:35:46.431280: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.431280:   - UID: E004015304F3DD22
2025-08-26 16:35:46.432277:   - Data: E004015304F3DD22
2025-08-26 16:35:46.432277:   - EventType: 1
2025-08-26 16:35:46.432277:   - Direction: 0
2025-08-26 16:35:46.432277:   - Antenna: 1
2025-08-26 16:35:46.432277:   - TagFrequency: 0
2025-08-26 16:35:46.432277: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.432277:   - UID: E004015305F68508
2025-08-26 16:35:46.432277:   - Data: E004015305F68508
2025-08-26 16:35:46.433274:   - EventType: 1
2025-08-26 16:35:46.433274:   - Direction: 0
2025-08-26 16:35:46.433274:   - Antenna: 1
2025-08-26 16:35:46.433274:   - TagFrequency: 0
2025-08-26 16:35:46.433274: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.433274:   - UID: E004015304F3DD22
2025-08-26 16:35:46.433274:   - Data: E004015304F3DD22
2025-08-26 16:35:46.433274:   - EventType: 1
2025-08-26 16:35:46.434270:   - Direction: 0
2025-08-26 16:35:46.434270:   - Antenna: 1
2025-08-26 16:35:46.434270:   - TagFrequency: 0
2025-08-26 16:35:46.587761: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-26 16:35:46.919661: 🔄 开始RFID轮询检查...
2025-08-26 16:35:46.919661: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:46.920658: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:46.920658: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:46.920658: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:46.920658: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:46.920658: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:46.923648: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:46.923648:   - 设备句柄: 2424588958144
2025-08-26 16:35:46.923648:   - FetchRecords返回值: 0
2025-08-26 16:35:46.923648:   - 报告数量: 5
2025-08-26 16:35:46.923648:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.923648:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:46.923648:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.924644:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.924644:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.924644:   - 数据长度: 8
2025-08-26 16:35:46.924644:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:46.924644:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:46.924644:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.924644:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:46.925641:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.925641:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.925641:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.925641:   - 数据长度: 8
2025-08-26 16:35:46.925641:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:46.925641:   - 提取的UID: E004015305F68508
2025-08-26 16:35:46.925641:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.925641:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:46.926637:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.926637:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.926637:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.926637:   - 数据长度: 8
2025-08-26 16:35:46.926637:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:46.927640:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:46.927640:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.927640:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:46.927640:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.927640:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.928632:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.928632:   - 数据长度: 8
2025-08-26 16:35:46.928632:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:46.928632:   - 提取的UID: E004015305F68508
2025-08-26 16:35:46.929629:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:46.929629:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:46.929629:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:46.929629:   - 设备类型: LSGControlCenter
2025-08-26 16:35:46.930625:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:46.930625:   - 数据长度: 8
2025-08-26 16:35:46.930625:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:46.930625:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:46.930625: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:46.930625:   - 发现标签数量: 5
2025-08-26 16:35:46.930625:   - 标签详情:
2025-08-26 16:35:46.930625:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:46.931621:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:46.931621:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:46.931621:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:46.931621:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:46.931621: RFID扫描: 发现 5 个标签
2025-08-26 16:35:46.931621: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:46.931621: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:46.932618: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.932618:   - UID: E004015304F3DD22
2025-08-26 16:35:46.932618:   - Data: E004015304F3DD22
2025-08-26 16:35:46.932618:   - EventType: 1
2025-08-26 16:35:46.932618:   - Direction: 0
2025-08-26 16:35:46.932618:   - Antenna: 1
2025-08-26 16:35:46.932618:   - TagFrequency: 0
2025-08-26 16:35:46.932618: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.933615:   - UID: E004015305F68508
2025-08-26 16:35:46.933615:   - Data: E004015305F68508
2025-08-26 16:35:46.933615:   - EventType: 1
2025-08-26 16:35:46.933615:   - Direction: 0
2025-08-26 16:35:46.933615:   - Antenna: 1
2025-08-26 16:35:46.933615:   - TagFrequency: 0
2025-08-26 16:35:46.933615: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.933615:   - UID: E004015304F3DD22
2025-08-26 16:35:46.934611:   - Data: E004015304F3DD22
2025-08-26 16:35:46.934611:   - EventType: 1
2025-08-26 16:35:46.934611:   - Direction: 0
2025-08-26 16:35:46.934611:   - Antenna: 1
2025-08-26 16:35:46.934611:   - TagFrequency: 0
2025-08-26 16:35:46.934611: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.934611:   - UID: E004015305F68508
2025-08-26 16:35:46.934611:   - Data: E004015305F68508
2025-08-26 16:35:46.935608:   - EventType: 1
2025-08-26 16:35:46.935608:   - Direction: 0
2025-08-26 16:35:46.935608:   - Antenna: 1
2025-08-26 16:35:46.935608:   - TagFrequency: 0
2025-08-26 16:35:46.935608: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:46.935608:   - UID: E004015304F3DD22
2025-08-26 16:35:46.935608:   - Data: E004015304F3DD22
2025-08-26 16:35:46.935608:   - EventType: 1
2025-08-26 16:35:46.936605:   - Direction: 0
2025-08-26 16:35:46.936605:   - Antenna: 1
2025-08-26 16:35:46.936605:   - TagFrequency: 0
2025-08-26 16:35:47.418629: 🔄 开始RFID轮询检查...
2025-08-26 16:35:47.418629: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:47.418629: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:47.418629: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:47.418629: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:47.419627: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:47.419627: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:47.422617: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:47.422617:   - 设备句柄: 2424588958144
2025-08-26 16:35:47.422617:   - FetchRecords返回值: 0
2025-08-26 16:35:47.423617:   - 报告数量: 5
2025-08-26 16:35:47.423617:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.423617:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:47.423617:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.423617:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.424610:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.424610:   - 数据长度: 8
2025-08-26 16:35:47.424610:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:47.424610:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:47.424610:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.424610:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:47.424610:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.425606:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.425606:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.425606:   - 数据长度: 8
2025-08-26 16:35:47.425606:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:47.425606:   - 提取的UID: E004015305F68508
2025-08-26 16:35:47.425606:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.425606:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:47.425606:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.426603:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.426603:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.426603:   - 数据长度: 8
2025-08-26 16:35:47.426603:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:47.426603:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:47.426603:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.426603:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:47.427599:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.427599:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.427599:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.427599:   - 数据长度: 8
2025-08-26 16:35:47.427599:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:47.427599:   - 提取的UID: E004015305F68508
2025-08-26 16:35:47.427599:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.427599:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:47.428596:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.428596:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.428596:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.428596:   - 数据长度: 8
2025-08-26 16:35:47.428596:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:47.428596:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:47.428596: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:47.428596:   - 发现标签数量: 5
2025-08-26 16:35:47.428596:   - 标签详情:
2025-08-26 16:35:47.429593:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:47.429593:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:47.429593:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:47.429593:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:47.429593:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:47.429593: RFID扫描: 发现 5 个标签
2025-08-26 16:35:47.429593: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:47.429593: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:47.430590: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.430590:   - UID: E004015304F3DD22
2025-08-26 16:35:47.430590:   - Data: E004015304F3DD22
2025-08-26 16:35:47.430590:   - EventType: 1
2025-08-26 16:35:47.430590:   - Direction: 0
2025-08-26 16:35:47.430590:   - Antenna: 1
2025-08-26 16:35:47.430590:   - TagFrequency: 0
2025-08-26 16:35:47.430590: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.431586:   - UID: E004015305F68508
2025-08-26 16:35:47.431586:   - Data: E004015305F68508
2025-08-26 16:35:47.431586:   - EventType: 1
2025-08-26 16:35:47.431586:   - Direction: 0
2025-08-26 16:35:47.431586:   - Antenna: 1
2025-08-26 16:35:47.431586:   - TagFrequency: 0
2025-08-26 16:35:47.431586: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.431586:   - UID: E004015304F3DD22
2025-08-26 16:35:47.432583:   - Data: E004015304F3DD22
2025-08-26 16:35:47.432583:   - EventType: 1
2025-08-26 16:35:47.432583:   - Direction: 0
2025-08-26 16:35:47.432583:   - Antenna: 1
2025-08-26 16:35:47.432583:   - TagFrequency: 0
2025-08-26 16:35:47.432583: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.432583:   - UID: E004015305F68508
2025-08-26 16:35:47.432583:   - Data: E004015305F68508
2025-08-26 16:35:47.433580:   - EventType: 1
2025-08-26 16:35:47.433580:   - Direction: 0
2025-08-26 16:35:47.433580:   - Antenna: 1
2025-08-26 16:35:47.433580:   - TagFrequency: 0
2025-08-26 16:35:47.433580: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.433580:   - UID: E004015304F3DD22
2025-08-26 16:35:47.433580:   - Data: E004015304F3DD22
2025-08-26 16:35:47.433580:   - EventType: 1
2025-08-26 16:35:47.434576:   - Direction: 0
2025-08-26 16:35:47.434576:   - Antenna: 1
2025-08-26 16:35:47.434576:   - TagFrequency: 0
2025-08-26 16:35:47.918970: 🔄 开始RFID轮询检查...
2025-08-26 16:35:47.918970: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:47.918970: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:47.918970: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:47.918970: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:47.919968: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:47.919968: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:47.922957: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:47.922957:   - 设备句柄: 2424588958144
2025-08-26 16:35:47.922957:   - FetchRecords返回值: 0
2025-08-26 16:35:47.922957:   - 报告数量: 5
2025-08-26 16:35:47.923960:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.923960:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:47.923960:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.923960:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.923960:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.924950:   - 数据长度: 8
2025-08-26 16:35:47.924950:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:47.924950:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:47.924950:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.924950:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:47.924950:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.925947:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.925947:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.925947:   - 数据长度: 8
2025-08-26 16:35:47.925947:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:47.925947:   - 提取的UID: E004015305F68508
2025-08-26 16:35:47.925947:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.925947:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:47.926944:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.926944:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.926944:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.926944:   - 数据长度: 8
2025-08-26 16:35:47.926944:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:47.926944:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:47.926944:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.927940:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:47.927940:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.927940:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.927940:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.927940:   - 数据长度: 8
2025-08-26 16:35:47.927940:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:47.927940:   - 提取的UID: E004015305F68508
2025-08-26 16:35:47.927940:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:47.928937:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:47.928937:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:47.928937:   - 设备类型: LSGControlCenter
2025-08-26 16:35:47.928937:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:47.928937:   - 数据长度: 8
2025-08-26 16:35:47.928937:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:47.928937:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:47.928937: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:47.929934:   - 发现标签数量: 5
2025-08-26 16:35:47.929934:   - 标签详情:
2025-08-26 16:35:47.929934:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:47.929934:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:47.929934:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:47.929934:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:47.929934:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:47.929934: RFID扫描: 发现 5 个标签
2025-08-26 16:35:47.930930: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:47.930930: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:47.930930: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.930930:   - UID: E004015304F3DD22
2025-08-26 16:35:47.930930:   - Data: E004015304F3DD22
2025-08-26 16:35:47.930930:   - EventType: 1
2025-08-26 16:35:47.931931:   - Direction: 0
2025-08-26 16:35:47.931931:   - Antenna: 1
2025-08-26 16:35:47.932928:   - TagFrequency: 0
2025-08-26 16:35:47.932928: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.932928:   - UID: E004015305F68508
2025-08-26 16:35:47.933926:   - Data: E004015305F68508
2025-08-26 16:35:47.933926:   - EventType: 1
2025-08-26 16:35:47.933926:   - Direction: 0
2025-08-26 16:35:47.934920:   - Antenna: 1
2025-08-26 16:35:47.934920:   - TagFrequency: 0
2025-08-26 16:35:47.934920: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.934920:   - UID: E004015304F3DD22
2025-08-26 16:35:47.935915:   - Data: E004015304F3DD22
2025-08-26 16:35:47.935915:   - EventType: 1
2025-08-26 16:35:47.935915:   - Direction: 0
2025-08-26 16:35:47.935915:   - Antenna: 1
2025-08-26 16:35:47.935915:   - TagFrequency: 0
2025-08-26 16:35:47.935915: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.936911:   - UID: E004015305F68508
2025-08-26 16:35:47.936911:   - Data: E004015305F68508
2025-08-26 16:35:47.936911:   - EventType: 1
2025-08-26 16:35:47.936911:   - Direction: 0
2025-08-26 16:35:47.936911:   - Antenna: 1
2025-08-26 16:35:47.936911:   - TagFrequency: 0
2025-08-26 16:35:47.936911: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:47.937908:   - UID: E004015304F3DD22
2025-08-26 16:35:47.937908:   - Data: E004015304F3DD22
2025-08-26 16:35:47.937908:   - EventType: 1
2025-08-26 16:35:47.937908:   - Direction: 0
2025-08-26 16:35:47.937908:   - Antenna: 1
2025-08-26 16:35:47.937908:   - TagFrequency: 0
2025-08-26 16:35:48.419311: 🔄 开始RFID轮询检查...
2025-08-26 16:35:48.419311: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:48.419311: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:48.419311: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:48.420308: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:48.420308: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:48.420308: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:48.422301: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:48.423297:   - 设备句柄: 2424588958144
2025-08-26 16:35:48.423297:   - FetchRecords返回值: 0
2025-08-26 16:35:48.423297:   - 报告数量: 5
2025-08-26 16:35:48.423297:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.423297:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:48.423297:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.424294:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.424294:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.424294:   - 数据长度: 8
2025-08-26 16:35:48.424294:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:48.424294:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:48.424294:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.424294:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:48.425291:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.425291:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.426291:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.426291:   - 数据长度: 8
2025-08-26 16:35:48.427286:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:48.427286:   - 提取的UID: E004015305F68508
2025-08-26 16:35:48.427286:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.427286:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:48.428281:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.428281:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.428281:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.428281:   - 数据长度: 8
2025-08-26 16:35:48.428281:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:48.428281:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:48.429278:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.429278:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:48.429278:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.429278:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.429278:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.429278:   - 数据长度: 8
2025-08-26 16:35:48.429278:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:48.429278:   - 提取的UID: E004015305F68508
2025-08-26 16:35:48.430274:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.430274:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:48.430274:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.430274:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.430274:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.430274:   - 数据长度: 8
2025-08-26 16:35:48.430274:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:48.430274:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:48.431271: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:48.431271:   - 发现标签数量: 5
2025-08-26 16:35:48.431271:   - 标签详情:
2025-08-26 16:35:48.431271:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:48.431271:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:48.431271:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:48.431271:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:48.431271:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:48.432268: RFID扫描: 发现 5 个标签
2025-08-26 16:35:48.432268: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:48.432268: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:48.432268: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.432268:   - UID: E004015304F3DD22
2025-08-26 16:35:48.432268:   - Data: E004015304F3DD22
2025-08-26 16:35:48.432268:   - EventType: 1
2025-08-26 16:35:48.432268:   - Direction: 0
2025-08-26 16:35:48.433264:   - Antenna: 1
2025-08-26 16:35:48.433264:   - TagFrequency: 0
2025-08-26 16:35:48.433264: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.433264:   - UID: E004015305F68508
2025-08-26 16:35:48.433264:   - Data: E004015305F68508
2025-08-26 16:35:48.433264:   - EventType: 1
2025-08-26 16:35:48.433264:   - Direction: 0
2025-08-26 16:35:48.433264:   - Antenna: 1
2025-08-26 16:35:48.434261:   - TagFrequency: 0
2025-08-26 16:35:48.434261: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.434261:   - UID: E004015304F3DD22
2025-08-26 16:35:48.434261:   - Data: E004015304F3DD22
2025-08-26 16:35:48.434261:   - EventType: 1
2025-08-26 16:35:48.434261:   - Direction: 0
2025-08-26 16:35:48.434261:   - Antenna: 1
2025-08-26 16:35:48.434261:   - TagFrequency: 0
2025-08-26 16:35:48.435258: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.435258:   - UID: E004015305F68508
2025-08-26 16:35:48.435258:   - Data: E004015305F68508
2025-08-26 16:35:48.435258:   - EventType: 1
2025-08-26 16:35:48.435258:   - Direction: 0
2025-08-26 16:35:48.435258:   - Antenna: 1
2025-08-26 16:35:48.435258:   - TagFrequency: 0
2025-08-26 16:35:48.436254: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.436254:   - UID: E004015304F3DD22
2025-08-26 16:35:48.436254:   - Data: E004015304F3DD22
2025-08-26 16:35:48.436254:   - EventType: 1
2025-08-26 16:35:48.436254:   - Direction: 0
2025-08-26 16:35:48.436254:   - Antenna: 1
2025-08-26 16:35:48.436254:   - TagFrequency: 0
2025-08-26 16:35:48.918657: 🔄 开始RFID轮询检查...
2025-08-26 16:35:48.919654: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:48.920653: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:48.920653: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:48.920653: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:48.920653: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:48.921647: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:48.922642: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:48.922642:   - 设备句柄: 2424588958144
2025-08-26 16:35:48.922642:   - FetchRecords返回值: 0
2025-08-26 16:35:48.922642:   - 报告数量: 5
2025-08-26 16:35:48.923639:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.923639:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:48.923639:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.923639:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.923639:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.923639:   - 数据长度: 8
2025-08-26 16:35:48.923639:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:48.924635:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:48.924635:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.924635:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:48.924635:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.924635:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.924635:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.924635:   - 数据长度: 8
2025-08-26 16:35:48.925632:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:48.925632:   - 提取的UID: E004015305F68508
2025-08-26 16:35:48.925632:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.925632:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:48.925632:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.925632:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.925632:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.925632:   - 数据长度: 8
2025-08-26 16:35:48.926629:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:48.926629:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:48.926629:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.926629:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:48.926629:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.926629:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.926629:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.926629:   - 数据长度: 8
2025-08-26 16:35:48.927625:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:48.927625:   - 提取的UID: E004015305F68508
2025-08-26 16:35:48.927625:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:48.927625:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:48.927625:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:48.927625:   - 设备类型: LSGControlCenter
2025-08-26 16:35:48.927625:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:48.927625:   - 数据长度: 8
2025-08-26 16:35:48.928622:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:48.928622:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:48.928622: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:48.928622:   - 发现标签数量: 5
2025-08-26 16:35:48.928622:   - 标签详情:
2025-08-26 16:35:48.928622:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:48.928622:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:48.928622:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:48.929619:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:48.929619:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:48.929619: RFID扫描: 发现 5 个标签
2025-08-26 16:35:48.929619: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:48.929619: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:48.929619: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.929619:   - UID: E004015304F3DD22
2025-08-26 16:35:48.929619:   - Data: E004015304F3DD22
2025-08-26 16:35:48.930615:   - EventType: 1
2025-08-26 16:35:48.930615:   - Direction: 0
2025-08-26 16:35:48.930615:   - Antenna: 1
2025-08-26 16:35:48.930615:   - TagFrequency: 0
2025-08-26 16:35:48.930615: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.930615:   - UID: E004015305F68508
2025-08-26 16:35:48.930615:   - Data: E004015305F68508
2025-08-26 16:35:48.930615:   - EventType: 1
2025-08-26 16:35:48.931612:   - Direction: 0
2025-08-26 16:35:48.931612:   - Antenna: 1
2025-08-26 16:35:48.931612:   - TagFrequency: 0
2025-08-26 16:35:48.931612: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.931612:   - UID: E004015304F3DD22
2025-08-26 16:35:48.931612:   - Data: E004015304F3DD22
2025-08-26 16:35:48.931612:   - EventType: 1
2025-08-26 16:35:48.931612:   - Direction: 0
2025-08-26 16:35:48.932609:   - Antenna: 1
2025-08-26 16:35:48.932609:   - TagFrequency: 0
2025-08-26 16:35:48.932609: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.932609:   - UID: E004015305F68508
2025-08-26 16:35:48.932609:   - Data: E004015305F68508
2025-08-26 16:35:48.932609:   - EventType: 1
2025-08-26 16:35:48.932609:   - Direction: 0
2025-08-26 16:35:48.932609:   - Antenna: 1
2025-08-26 16:35:48.933606:   - TagFrequency: 0
2025-08-26 16:35:48.933606: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:48.933606:   - UID: E004015304F3DD22
2025-08-26 16:35:48.933606:   - Data: E004015304F3DD22
2025-08-26 16:35:48.933606:   - EventType: 1
2025-08-26 16:35:48.933606:   - Direction: 0
2025-08-26 16:35:48.933606:   - Antenna: 1
2025-08-26 16:35:48.933606:   - TagFrequency: 0
2025-08-26 16:35:49.418996: 🔄 开始RFID轮询检查...
2025-08-26 16:35:49.418996: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:49.418996: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:49.418996: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:49.418996: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:49.419993: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:49.419993: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:49.422984: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:49.422984:   - 设备句柄: 2424588958144
2025-08-26 16:35:49.423980:   - FetchRecords返回值: 0
2025-08-26 16:35:49.423980:   - 报告数量: 5
2025-08-26 16:35:49.423980:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.423980:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:49.423980:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.423980:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.423980:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.424976:   - 数据长度: 8
2025-08-26 16:35:49.424976:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:49.424976:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:49.424976:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.424976:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:49.424976:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.424976:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.425973:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.425973:   - 数据长度: 8
2025-08-26 16:35:49.425973:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:49.425973:   - 提取的UID: E004015305F68508
2025-08-26 16:35:49.425973:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.425973:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:49.425973:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.425973:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.426970:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.426970:   - 数据长度: 8
2025-08-26 16:35:49.426970:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:49.426970:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:49.426970:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.426970:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:49.426970:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.426970:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.427966:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.427966:   - 数据长度: 8
2025-08-26 16:35:49.427966:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:49.427966:   - 提取的UID: E004015305F68508
2025-08-26 16:35:49.427966:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.427966:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:49.427966:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.427966:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.428963:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.428963:   - 数据长度: 8
2025-08-26 16:35:49.428963:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:49.428963:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:49.428963: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:49.429960:   - 发现标签数量: 5
2025-08-26 16:35:49.429960:   - 标签详情:
2025-08-26 16:35:49.430962:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:49.430962:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:49.430962:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:49.431959:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:49.431959:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:49.432951: RFID扫描: 发现 5 个标签
2025-08-26 16:35:49.432951: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:49.432951: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:49.432951: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.433952:   - UID: E004015304F3DD22
2025-08-26 16:35:49.433952:   - Data: E004015304F3DD22
2025-08-26 16:35:49.433952:   - EventType: 1
2025-08-26 16:35:49.433952:   - Direction: 0
2025-08-26 16:35:49.433952:   - Antenna: 1
2025-08-26 16:35:49.434943:   - TagFrequency: 0
2025-08-26 16:35:49.434943: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.434943:   - UID: E004015305F68508
2025-08-26 16:35:49.434943:   - Data: E004015305F68508
2025-08-26 16:35:49.434943:   - EventType: 1
2025-08-26 16:35:49.434943:   - Direction: 0
2025-08-26 16:35:49.435940:   - Antenna: 1
2025-08-26 16:35:49.435940:   - TagFrequency: 0
2025-08-26 16:35:49.435940: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.435940:   - UID: E004015304F3DD22
2025-08-26 16:35:49.435940:   - Data: E004015304F3DD22
2025-08-26 16:35:49.436937:   - EventType: 1
2025-08-26 16:35:49.436937:   - Direction: 0
2025-08-26 16:35:49.436937:   - Antenna: 1
2025-08-26 16:35:49.436937:   - TagFrequency: 0
2025-08-26 16:35:49.436937: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.436937:   - UID: E004015305F68508
2025-08-26 16:35:49.437933:   - Data: E004015305F68508
2025-08-26 16:35:49.437933:   - EventType: 1
2025-08-26 16:35:49.437933:   - Direction: 0
2025-08-26 16:35:49.437933:   - Antenna: 1
2025-08-26 16:35:49.437933:   - TagFrequency: 0
2025-08-26 16:35:49.437933: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.437933:   - UID: E004015304F3DD22
2025-08-26 16:35:49.438930:   - Data: E004015304F3DD22
2025-08-26 16:35:49.438930:   - EventType: 1
2025-08-26 16:35:49.438930:   - Direction: 0
2025-08-26 16:35:49.438930:   - Antenna: 1
2025-08-26 16:35:49.438930:   - TagFrequency: 0
2025-08-26 16:35:49.588434: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-26 16:35:49.588434: 多认证管理器状态变更: listening
2025-08-26 16:35:49.588434: ⚠️ 人脸识别服务未初始化或不可用
2025-08-26 16:35:49.919337: 🔄 开始RFID轮询检查...
2025-08-26 16:35:49.919337: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:49.919337: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:49.919337: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:49.919337: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:49.920334: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:49.920334: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:49.922327: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:49.923327:   - 设备句柄: 2424588958144
2025-08-26 16:35:49.924322:   - FetchRecords返回值: 0
2025-08-26 16:35:49.924322:   - 报告数量: 5
2025-08-26 16:35:49.925318:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.925318:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:49.925318:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.925318:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.926314:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.926314:   - 数据长度: 8
2025-08-26 16:35:49.926314:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:49.926314:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:49.926314:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.926314:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:49.927310:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.927310:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.927310:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.927310:   - 数据长度: 8
2025-08-26 16:35:49.927310:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:49.927310:   - 提取的UID: E004015305F68508
2025-08-26 16:35:49.927310:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.928307:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:49.928307:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.928307:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.928307:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.928307:   - 数据长度: 8
2025-08-26 16:35:49.928307:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:49.928307:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:49.928307:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.929303:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:49.929303:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.929303:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.929303:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.929303:   - 数据长度: 8
2025-08-26 16:35:49.929303:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:49.929303:   - 提取的UID: E004015305F68508
2025-08-26 16:35:49.929303:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:49.930300:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:49.930300:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:49.930300:   - 设备类型: LSGControlCenter
2025-08-26 16:35:49.930300:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:49.930300:   - 数据长度: 8
2025-08-26 16:35:49.930300:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:49.930300:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:49.930300: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:49.931297:   - 发现标签数量: 5
2025-08-26 16:35:49.931297:   - 标签详情:
2025-08-26 16:35:49.931297:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:49.931297:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:49.931297:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:49.931297:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:49.931297:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:49.931297: RFID扫描: 发现 5 个标签
2025-08-26 16:35:49.932294: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:49.932294: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:49.932294: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.932294:   - UID: E004015304F3DD22
2025-08-26 16:35:49.932294:   - Data: E004015304F3DD22
2025-08-26 16:35:49.932294:   - EventType: 1
2025-08-26 16:35:49.932294:   - Direction: 0
2025-08-26 16:35:49.932294:   - Antenna: 1
2025-08-26 16:35:49.933290:   - TagFrequency: 0
2025-08-26 16:35:49.933290: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.933290:   - UID: E004015305F68508
2025-08-26 16:35:49.933290:   - Data: E004015305F68508
2025-08-26 16:35:49.933290:   - EventType: 1
2025-08-26 16:35:49.933290:   - Direction: 0
2025-08-26 16:35:49.933290:   - Antenna: 1
2025-08-26 16:35:49.933290:   - TagFrequency: 0
2025-08-26 16:35:49.934287: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.934287:   - UID: E004015304F3DD22
2025-08-26 16:35:49.934287:   - Data: E004015304F3DD22
2025-08-26 16:35:49.934287:   - EventType: 1
2025-08-26 16:35:49.934287:   - Direction: 0
2025-08-26 16:35:49.934287:   - Antenna: 1
2025-08-26 16:35:49.934287:   - TagFrequency: 0
2025-08-26 16:35:49.934287: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.935284:   - UID: E004015305F68508
2025-08-26 16:35:49.935284:   - Data: E004015305F68508
2025-08-26 16:35:49.935284:   - EventType: 1
2025-08-26 16:35:49.935284:   - Direction: 0
2025-08-26 16:35:49.935284:   - Antenna: 1
2025-08-26 16:35:49.935284:   - TagFrequency: 0
2025-08-26 16:35:49.935284: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:49.935284:   - UID: E004015304F3DD22
2025-08-26 16:35:49.936280:   - Data: E004015304F3DD22
2025-08-26 16:35:49.936280:   - EventType: 1
2025-08-26 16:35:49.936280:   - Direction: 0
2025-08-26 16:35:49.936280:   - Antenna: 1
2025-08-26 16:35:49.936280:   - TagFrequency: 0
2025-08-26 16:35:50.419195: 🔄 开始RFID轮询检查...
2025-08-26 16:35:50.419195: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:50.420193: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:50.420193: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:50.420193: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:50.420193: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:50.420193: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:50.422185: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:50.423182:   - 设备句柄: 2424588958144
2025-08-26 16:35:50.423182:   - FetchRecords返回值: 0
2025-08-26 16:35:50.423182:   - 报告数量: 5
2025-08-26 16:35:50.423182:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.423182:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:50.423182:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.424178:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.424178:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.424178:   - 数据长度: 8
2025-08-26 16:35:50.424178:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:50.424178:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:50.424178:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.424178:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:50.424178:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.425175:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.425175:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.425175:   - 数据长度: 8
2025-08-26 16:35:50.425175:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:50.425175:   - 提取的UID: E004015305F68508
2025-08-26 16:35:50.425175:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.425175:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:50.425175:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.426172:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.426172:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.426172:   - 数据长度: 8
2025-08-26 16:35:50.426172:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:50.426172:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:50.426172:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.426172:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:50.426172:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.427169:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.427169:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.427169:   - 数据长度: 8
2025-08-26 16:35:50.427169:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:50.427169:   - 提取的UID: E004015305F68508
2025-08-26 16:35:50.427169:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.427169:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:50.427169:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.428165:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.428165:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.428165:   - 数据长度: 8
2025-08-26 16:35:50.428165:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:50.428165:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:50.428165: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:50.428165:   - 发现标签数量: 5
2025-08-26 16:35:50.428165:   - 标签详情:
2025-08-26 16:35:50.429162:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:50.429162:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:50.429162:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:50.429162:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:50.429162:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:50.429162: RFID扫描: 发现 5 个标签
2025-08-26 16:35:50.429162: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:50.429162: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:50.430159: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.430159:   - UID: E004015304F3DD22
2025-08-26 16:35:50.430159:   - Data: E004015304F3DD22
2025-08-26 16:35:50.430159:   - EventType: 1
2025-08-26 16:35:50.430159:   - Direction: 0
2025-08-26 16:35:50.430159:   - Antenna: 1
2025-08-26 16:35:50.430159:   - TagFrequency: 0
2025-08-26 16:35:50.430159: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.430159:   - UID: E004015305F68508
2025-08-26 16:35:50.431155:   - Data: E004015305F68508
2025-08-26 16:35:50.431155:   - EventType: 1
2025-08-26 16:35:50.431155:   - Direction: 0
2025-08-26 16:35:50.431155:   - Antenna: 1
2025-08-26 16:35:50.431155:   - TagFrequency: 0
2025-08-26 16:35:50.431155: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.431155:   - UID: E004015304F3DD22
2025-08-26 16:35:50.431155:   - Data: E004015304F3DD22
2025-08-26 16:35:50.432152:   - EventType: 1
2025-08-26 16:35:50.432152:   - Direction: 0
2025-08-26 16:35:50.432152:   - Antenna: 1
2025-08-26 16:35:50.432152:   - TagFrequency: 0
2025-08-26 16:35:50.432152: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.432152:   - UID: E004015305F68508
2025-08-26 16:35:50.432152:   - Data: E004015305F68508
2025-08-26 16:35:50.432152:   - EventType: 1
2025-08-26 16:35:50.433149:   - Direction: 0
2025-08-26 16:35:50.433149:   - Antenna: 1
2025-08-26 16:35:50.433149:   - TagFrequency: 0
2025-08-26 16:35:50.433149: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.433149:   - UID: E004015304F3DD22
2025-08-26 16:35:50.433149:   - Data: E004015304F3DD22
2025-08-26 16:35:50.433149:   - EventType: 1
2025-08-26 16:35:50.433149:   - Direction: 0
2025-08-26 16:35:50.434145:   - Antenna: 1
2025-08-26 16:35:50.434145:   - TagFrequency: 0
2025-08-26 16:35:50.918539: 🔄 开始RFID轮询检查...
2025-08-26 16:35:50.919536: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:50.919536: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:50.919536: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:50.920533: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:50.920533: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:50.920533: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:50.922526: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:50.922526:   - 设备句柄: 2424588958144
2025-08-26 16:35:50.922526:   - FetchRecords返回值: 0
2025-08-26 16:35:50.922526:   - 报告数量: 5
2025-08-26 16:35:50.922526:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.922526:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:50.922526:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.923523:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.923523:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.923523:   - 数据长度: 8
2025-08-26 16:35:50.923523:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:50.923523:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:50.923523:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.923523:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:50.924519:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.924519:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.924519:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.924519:   - 数据长度: 8
2025-08-26 16:35:50.924519:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:50.924519:   - 提取的UID: E004015305F68508
2025-08-26 16:35:50.924519:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.924519:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:50.925516:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.925516:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.925516:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.925516:   - 数据长度: 8
2025-08-26 16:35:50.925516:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:50.925516:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:50.925516:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.926513:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:50.926513:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.926513:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.926513:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.926513:   - 数据长度: 8
2025-08-26 16:35:50.926513:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:50.926513:   - 提取的UID: E004015305F68508
2025-08-26 16:35:50.926513:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:50.927509:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:50.927509:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:50.927509:   - 设备类型: LSGControlCenter
2025-08-26 16:35:50.928509:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:50.928509:   - 数据长度: 8
2025-08-26 16:35:50.928509:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:50.929510:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:50.929510: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:50.929510:   - 发现标签数量: 5
2025-08-26 16:35:50.929510:   - 标签详情:
2025-08-26 16:35:50.930504:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:50.930504:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:50.930504:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:50.930504:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:50.930504:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:50.931500: RFID扫描: 发现 5 个标签
2025-08-26 16:35:50.931500: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:50.931500: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:50.931500: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.931500:   - UID: E004015304F3DD22
2025-08-26 16:35:50.932493:   - Data: E004015304F3DD22
2025-08-26 16:35:50.932493:   - EventType: 1
2025-08-26 16:35:50.932493:   - Direction: 0
2025-08-26 16:35:50.932493:   - Antenna: 1
2025-08-26 16:35:50.932493:   - TagFrequency: 0
2025-08-26 16:35:50.932493: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.933490:   - UID: E004015305F68508
2025-08-26 16:35:50.933490:   - Data: E004015305F68508
2025-08-26 16:35:50.933490:   - EventType: 1
2025-08-26 16:35:50.933490:   - Direction: 0
2025-08-26 16:35:50.933490:   - Antenna: 1
2025-08-26 16:35:50.933490:   - TagFrequency: 0
2025-08-26 16:35:50.933490: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.934486:   - UID: E004015304F3DD22
2025-08-26 16:35:50.934486:   - Data: E004015304F3DD22
2025-08-26 16:35:50.934486:   - EventType: 1
2025-08-26 16:35:50.934486:   - Direction: 0
2025-08-26 16:35:50.934486:   - Antenna: 1
2025-08-26 16:35:50.934486:   - TagFrequency: 0
2025-08-26 16:35:50.934486: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.935483:   - UID: E004015305F68508
2025-08-26 16:35:50.935483:   - Data: E004015305F68508
2025-08-26 16:35:50.935483:   - EventType: 1
2025-08-26 16:35:50.935483:   - Direction: 0
2025-08-26 16:35:50.935483:   - Antenna: 1
2025-08-26 16:35:50.935483:   - TagFrequency: 0
2025-08-26 16:35:50.935483: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:50.936480:   - UID: E004015304F3DD22
2025-08-26 16:35:50.936480:   - Data: E004015304F3DD22
2025-08-26 16:35:50.936480:   - EventType: 1
2025-08-26 16:35:50.936480:   - Direction: 0
2025-08-26 16:35:50.936480:   - Antenna: 1
2025-08-26 16:35:50.936480:   - TagFrequency: 0
2025-08-26 16:35:51.418880: 🔄 开始RFID轮询检查...
2025-08-26 16:35:51.418880: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:51.418880: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:51.418880: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:51.418880: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:51.419877: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:51.419877: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:51.422867: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:51.422867:   - 设备句柄: 2424588958144
2025-08-26 16:35:51.423864:   - FetchRecords返回值: 0
2025-08-26 16:35:51.423864:   - 报告数量: 5
2025-08-26 16:35:51.423864:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.423864:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:51.423864:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.424860:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.424860:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.424860:   - 数据长度: 8
2025-08-26 16:35:51.424860:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:51.424860:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:51.424860:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.424860:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:51.424860:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.425856:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.425856:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.425856:   - 数据长度: 8
2025-08-26 16:35:51.425856:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:51.425856:   - 提取的UID: E004015305F68508
2025-08-26 16:35:51.425856:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.425856:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:51.426853:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.426853:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.426853:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.426853:   - 数据长度: 8
2025-08-26 16:35:51.426853:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:51.426853:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:51.426853:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.426853:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:51.427850:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.427850:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.427850:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.427850:   - 数据长度: 8
2025-08-26 16:35:51.427850:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:51.427850:   - 提取的UID: E004015305F68508
2025-08-26 16:35:51.427850:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.427850:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:51.428847:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.428847:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.428847:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.428847:   - 数据长度: 8
2025-08-26 16:35:51.428847:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:51.428847:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:51.428847: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:51.428847:   - 发现标签数量: 5
2025-08-26 16:35:51.429843:   - 标签详情:
2025-08-26 16:35:51.429843:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:51.429843:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:51.429843:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:51.429843:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:51.429843:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:51.429843: RFID扫描: 发现 5 个标签
2025-08-26 16:35:51.429843: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:51.430840: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:51.430840: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.430840:   - UID: E004015304F3DD22
2025-08-26 16:35:51.430840:   - Data: E004015304F3DD22
2025-08-26 16:35:51.430840:   - EventType: 1
2025-08-26 16:35:51.430840:   - Direction: 0
2025-08-26 16:35:51.430840:   - Antenna: 1
2025-08-26 16:35:51.431837:   - TagFrequency: 0
2025-08-26 16:35:51.431837: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.431837:   - UID: E004015305F68508
2025-08-26 16:35:51.431837:   - Data: E004015305F68508
2025-08-26 16:35:51.431837:   - EventType: 1
2025-08-26 16:35:51.431837:   - Direction: 0
2025-08-26 16:35:51.431837:   - Antenna: 1
2025-08-26 16:35:51.431837:   - TagFrequency: 0
2025-08-26 16:35:51.431837: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.432833:   - UID: E004015304F3DD22
2025-08-26 16:35:51.432833:   - Data: E004015304F3DD22
2025-08-26 16:35:51.432833:   - EventType: 1
2025-08-26 16:35:51.432833:   - Direction: 0
2025-08-26 16:35:51.432833:   - Antenna: 1
2025-08-26 16:35:51.432833:   - TagFrequency: 0
2025-08-26 16:35:51.432833: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.432833:   - UID: E004015305F68508
2025-08-26 16:35:51.433830:   - Data: E004015305F68508
2025-08-26 16:35:51.433830:   - EventType: 1
2025-08-26 16:35:51.433830:   - Direction: 0
2025-08-26 16:35:51.433830:   - Antenna: 1
2025-08-26 16:35:51.433830:   - TagFrequency: 0
2025-08-26 16:35:51.433830: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.433830:   - UID: E004015304F3DD22
2025-08-26 16:35:51.433830:   - Data: E004015304F3DD22
2025-08-26 16:35:51.434827:   - EventType: 1
2025-08-26 16:35:51.434827:   - Direction: 0
2025-08-26 16:35:51.434827:   - Antenna: 1
2025-08-26 16:35:51.434827:   - TagFrequency: 0
2025-08-26 16:35:51.551440: ⏰ 10秒数据收集完成，开始书籍检查... 时间: 2025-08-26 16:35:51.551440
2025-08-26 16:35:51.551440: 🛑 停止出馆数据收集，收集到UID数量: 0
2025-08-26 16:35:51.551440: 🔧 停止时间: 2025-08-26 16:35:51.551440
2025-08-26 16:35:51.552437: 📡 停止从共享池收集数据...
2025-08-26 16:35:51.552437: 🔧 保持RFID扫描运行，只停止从共享池收集数据
2025-08-26 16:35:51.552437: 📡 开始从RFID服务收集UID数据...
2025-08-26 16:35:51.552437: 📊 从HWTagProvider获取UID，标签数量: 2
2025-08-26 16:35:51.552437: 📡 收集到UID: E004015304F3DD22 (条码: null)
2025-08-26 16:35:51.552437: 📡 收集到UID: E004015305F68508 (条码: null)
2025-08-26 16:35:51.552437: 📡 从RFID服务收集到UID数量: 2
2025-08-26 16:35:51.552437: 📋 UID列表: E004015304F3DD22, E004015305F68508
2025-08-26 16:35:51.552437: 📊 数据收集完成，UID列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:35:51.553434: 🔍 开始三步书籍检查，UID数量: 2
2025-08-26 16:35:51.553434: 闸机状态变更: GateState.exitScanning -> GateState.exitChecking
2025-08-26 16:35:51.553434: 闸机状态更新: GateState.exitScanning -> GateState.exitChecking
2025-08-26 16:35:51.553434: 🚀 开始完整的三步书籍检查流程，UID数量: 2
2025-08-26 16:35:51.553434: 🔍 第一步：检查白名单，UID数量: 2
2025-08-26 16:35:51.553434: 🔍 输入UID列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:35:51.553434: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:35:51.553434: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:35:51.553434: 闸机状态变更: GateState.exitChecking
2025-08-26 16:35:51.554430: 🎨 处理状态变更UI: exitChecking
2025-08-26 16:35:51.554430: 未处理的状态变更UI: exitChecking
2025-08-26 16:35:51.554430: 
2025-08-26 16:35:51.554430: 📤 ========== 白名单检查接口 ==========
2025-08-26 16:35:51.554430: 📤 接口名称: 查询图书是否在白名单
2025-08-26 16:35:51.555427: 📤 请求URL: http://166.111.121.100:9000/tunano/ldc/white/tag/whitelist/contain
2025-08-26 16:35:51.555427: 📤 请求方法: POST
2025-08-26 16:35:51.555427: 📤 请求头: Content-Type: application/json
2025-08-26 16:35:51.555427: 📤 请求参数: {"Taglist":[{"Tid":"E004015304F3DD22"},{"Tid":"E004015305F68508"}]}
2025-08-26 16:35:51.555427: 📤 请求参数解析:
2025-08-26 16:35:51.555427: 📤   - Taglist: 标签列表，包含2个UID
2025-08-26 16:35:51.555427: 📤     [0] Tid: E004015304F3DD22
2025-08-26 16:35:51.555427: 📤     [1] Tid: E004015305F68508
2025-08-26 16:35:51.555427: 📤 =====================================
2025-08-26 16:35:51.555427: 
2025-08-26 16:35:51.562404: 
2025-08-26 16:35:51.563402: 📥 ========== 白名单检查响应 ==========
2025-08-26 16:35:51.563402: 📥 响应状态码: 200
2025-08-26 16:35:51.563402: 📥 响应原始数据: {"errorcode":-1,"message":"白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms","result":null}
2025-08-26 16:35:51.563402: 
2025-08-26 16:35:51.563402: 📥 响应数据解析:
2025-08-26 16:35:51.563402: 📥   - errorcode: -1 (0=在白名单内, 其他=不在白名单内)
2025-08-26 16:35:51.564397: 📥   - message: 白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms
2025-08-26 16:35:51.564397: 📥   - result: null
2025-08-26 16:35:51.564397: 🔍 解析白名单错误消息: 白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms
2025-08-26 16:35:51.564397: 🔍 方法1提取到的UID: E004015304F3DD22
2025-08-26 16:35:51.564397: ⚠️ 白名单检查结果: 有书籍不在白名单内
2025-08-26 16:35:51.564397: ⚠️ 不在白名单的UID字符串: E004015304F3DD22
2025-08-26 16:35:51.564397: ⚠️ 返回值: E004015304F3DD22
2025-08-26 16:35:51.565394: 📥 =====================================
2025-08-26 16:35:51.565394: 
2025-08-26 16:35:51.565394: 🔍 第二步：UID转条码，UID数量: 1
2025-08-26 16:35:51.565394: 🔍 输入UID字符串: E004015304F3DD22
2025-08-26 16:35:51.565394: 🔍 解析后UID列表: [E004015304F3DD22]
2025-08-26 16:35:51.565394: 
2025-08-26 16:35:51.566391: 📤 ========== UID转条码接口 (1/1) ==========
2025-08-26 16:35:51.566391: 📤 接口名称: 根据标签UID查询条码
2025-08-26 16:35:51.566391: 📤 请求URL: http://166.111.121.100:9000/tunano/rfdc/tag/v1/rfdc/tag/booktag/LibraryCode/CN-100084-QHDXLIB/Uid/E004015304F3DD22
2025-08-26 16:35:51.566391: 📤 请求方法: GET
2025-08-26 16:35:51.566391: 📤 路径参数:
2025-08-26 16:35:51.566391: 📤   - libraryCode: 从URL中提取的馆代码
2025-08-26 16:35:51.566391: 📤   - Uid: E004015304F3DD22 (标签UID/TID)
2025-08-26 16:35:51.566391: 📤 =====================================
2025-08-26 16:35:51.566391: 
2025-08-26 16:35:51.585329: 
2025-08-26 16:35:51.585329: 📥 ========== UID转条码响应 (1/1) ==========
2025-08-26 16:35:51.585329: 📥 UID: E004015304F3DD22
2025-08-26 16:35:51.585329: 📥 响应状态码: 200
2025-08-26 16:35:51.586325: 📥 响应原始数据: {"id":"2025352616084348048846","requestObject":"rfdc.tag","operation":"getbooktagbyuid","errorCode":0,"message":"查询成功","result":{"libraryCode":"CN-100084-QHDXLIB","uid":"E004015304F3DD22","barcode":"C3695488J","barcodeRaw":null,"userRaw":null,"updated":"2025-03-06 12:25:09","created":"2025-03-06 12:25:09"}}
2025-08-26 16:35:51.586325: 
2025-08-26 16:35:51.586325: 📥 响应数据解析:
2025-08-26 16:35:51.586325: 📥   - id: 2025352616084348048846
2025-08-26 16:35:51.586325: 📥   - requestObject: rfdc.tag
2025-08-26 16:35:51.586325: 📥   - operation: getbooktagbyuid
2025-08-26 16:35:51.586325: 📥   - errorCode: 0 (0=成功, 其他=失败)
2025-08-26 16:35:51.587321: 📥   - message: 查询成功
2025-08-26 16:35:51.587321: 📥   - result:
2025-08-26 16:35:51.587321: 📥     * libraryCode: CN-100084-QHDXLIB
2025-08-26 16:35:51.587321: 📥     * uid: E004015304F3DD22
2025-08-26 16:35:51.587321: 📥     * barcode: C3695488J
2025-08-26 16:35:51.587321: 📥     * updated: 2025-03-06 12:25:09
2025-08-26 16:35:51.587321: 📥     * created: 2025-03-06 12:25:09
2025-08-26 16:35:51.588318: ✅ UID转条码成功: E004015304F3DD22 → C3695488J
2025-08-26 16:35:51.588318: 📥 =====================================
2025-08-26 16:35:51.588318: 
2025-08-26 16:35:51.588318: ✅ 第二步完成，获得条码数量: 1
2025-08-26 16:35:51.588318: ✅ 获得的条码列表: [C3695488J]
2025-08-26 16:35:51.588318: 🔍 第三步：获取图书信息，条码数量: 1
2025-08-26 16:35:51.588318: 🔍 输入条码列表: [C3695488J]
2025-08-26 16:35:51.589315: 
2025-08-26 16:35:51.589315: 📤 ========== 获取图书信息接口 (1/1) ==========
2025-08-26 16:35:51.589315: 📤 接口名称: 获取图书信息GET
2025-08-26 16:35:51.589315: 📤 请求URL: http://166.111.121.100:9000/tunano/cdc/acs/v1/cdc/acs/queryBookInfo?LibraryCode=CN-100084-QHDXLIB&Mac=FF-FF-FF-FF-FF-FF&BookSn=C3695488J
2025-08-26 16:35:51.589315: 📤 请求方法: GET
2025-08-26 16:35:51.589315: 📤 查询参数:
2025-08-26 16:35:51.589315: 📤   - barcode: C3695488J (图书条码)
2025-08-26 16:35:51.590311: 📤 =====================================
2025-08-26 16:35:51.590311: 
2025-08-26 16:35:51.774700: 
2025-08-26 16:35:51.774700: 📥 ========== 图书信息响应 (1/1) ==========
2025-08-26 16:35:51.774700: 📥 条码: C3695488J
2025-08-26 16:35:51.774700: 📥 响应状态码: 200
2025-08-26 16:35:51.774700: 📥 响应原始数据: {"id":"2025352616084352015786","requestObject":"cdc.acs","operation":"querybookinfo","errorCode":0,"message":"查询成功","result":{"bookSn":"C3695488J","bookTitle":"生命科学中的非决定论争论 . ＝ The indeterminism debate in the life sciences","author":null,"publisher":null,"isbn":null,"callNumber":null,"keywords":null,"permanentLocation":"主馆 - mn125-lib","currentLocation":"主馆","currentShelfLocation":null,"circulationStatus":1,"circulationStatusValue":"03","price":0.0,"page":0,"circulationType":0,"needReturnDate":null,"needPickUpDate":null,"borrowedDate":null,"borrowFlag":false,"borrowPastronSn":null,"bookingFlag":false,"bookingPatronSnList":[],"message":"查询成功"}}
2025-08-26 16:35:51.775697: 
2025-08-26 16:35:51.775697: 📥 响应数据解析:
2025-08-26 16:35:51.775697: 📥   - errorCode: 0 (0=成功, 其他=失败)
2025-08-26 16:35:51.775697: 📥   - message: 查询成功
2025-08-26 16:35:51.775697: 📥   - result:
2025-08-26 16:35:51.775697: 📥     * bookSn: C3695488J (图书条码)
2025-08-26 16:35:51.775697: 📥     * bookTitle: 生命科学中的非决定论争论 . ＝ The indeterminism debate in the life sciences (书名)
2025-08-26 16:35:51.775697: 📥     * author: null (作者)
2025-08-26 16:35:51.775697: 📥     * isbn: null (ISBN)
2025-08-26 16:35:51.775697: 📥     * callNumber: null (索书号)
2025-08-26 16:35:51.776693: 📥     * publisher: null (出版社)
2025-08-26 16:35:51.776693: 📥     * borrowFlag: false (借阅标志)
2025-08-26 16:35:51.776693: 📥     * bookingFlag: false (预约标志)
2025-08-26 16:35:51.776693: 📥     * circulationStatus: 1 (流通状态)
2025-08-26 16:35:51.776693: 📥     * message: 查询成功 (消息)
2025-08-26 16:35:51.776693: ✅ 图书信息获取成功: C3695488J → 生命科学中的非决定论争论 . ＝ The indeterminism debate in the life sciences
2025-08-26 16:35:51.776693: 📥 =====================================
2025-08-26 16:35:51.776693: 
2025-08-26 16:35:51.776693: ✅ 第三步完成，获得图书信息数量: 1
2025-08-26 16:35:51.777690: ✅ 获得的图书信息列表:
2025-08-26 16:35:51.777690: ✅   [1] C3695488J - 生命科学中的非决定论争论 . ＝ The indeterminism debate in the life sciences (未知作者)
2025-08-26 16:35:51.777690: ✅ 三步检查完成: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:35:51.777690: ✅ 三步检查完成: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:35:51.777690: 📊 检查结果: 允许通过=true, 不在白名单书籍数量=1
2025-08-26 16:35:51.777690: ✅ 允许出馆: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:35:51.777690: ✅ 命令顺序正确：出馆流程中且最后命令是到位信号
2025-08-26 16:35:51.777690: 📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36
2025-08-26 16:35:51.777690: 📤 准备发送闸机命令: success_signal
2025-08-26 16:35:51.778692: 📤 成功信号已发送（异步）
2025-08-26 16:35:51.778692: 闸机状态变更: GateState.exitChecking -> GateState.exitOver
2025-08-26 16:35:51.778692: 闸机状态更新: GateState.exitChecking -> GateState.exitOver
2025-08-26 16:35:51.779687: 发送原始命令数据: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.779687: 发送原始数据: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.779687: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.779687: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:35:51.780681: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:35:51.780681: 闸机状态变更: GateState.exitOver
2025-08-26 16:35:51.780681: 🎨 处理状态变更UI: exitOver
2025-08-26 16:35:51.780681: 未处理的状态变更UI: exitOver
2025-08-26 16:35:51.781678: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.781678: 闸机命令发送成功: success_signal
2025-08-26 16:35:51.781678: ✅ 闸机命令 success_signal 发送成功
2025-08-26 16:35:51.781678: [channel_1] 收到闸机事件: exit_allowed
2025-08-26 16:35:51.781678: 📨 收到GateCoordinator事件: exit_allowed
2025-08-26 16:35:51.781678: Error:type 'Null' is not a subtype of type 'String' in type cast
Stack：#0      new BookInfo.fromJson (package:a3g/features/security_gate/models/book_info.dart:29)
#1      SilencePageViewModel._handleGateEvent.<anonymous closure> (package:a3g/features/security_gate/viewmodels/silence_page_viewmodel.dart:298)
#2      MappedListIterable.elementAt (dart:_internal/iterable.dart:415)
#3      ListIterator.moveNext (dart:_internal/iterable.dart:344)
#4      new _GrowableList._ofEfficientLengthIterable (dart:core-patch/growable_array.dart:189)
#5      new _GrowableList.of (dart:core-patch/growable_array.dart:150)
#6      new List.of (dart:core-patch/array_patch.dart:47)
#7      SetBase.toList (dart:collection/set.dart:119)
#8      SilencePageViewModel._handleGateEvent (package:a3g/features/security_gate/viewmodels/silence_page_viewmodel.dart:298)
#9      _rootRunUnary (dart:async/zone.dart:1407)
#10     _CustomZone.runUnary (dart:async/zone.dart:1308)
#11     _CustomZone.runUnaryGuarded (dart:async/zone.dart:1217)
#12     _BufferingStreamSubscription._sendData (dart:async/stream_impl.dart:339)
#13     _DelayedData.perform (dart:async/stream_impl.dart:515)
#14     _PendingEvents.handleNext (dart:async/stream_impl.dart:620)
#15     _PendingEvents.schedule.<anonymous closure> (dart:async/stream_impl.dart:591)
#16     _rootRun (dart:async/zone.dart:1391)
#17     _CustomZone.run (dart:async/zone.dart:1301)
#18     _CustomZone.runGuarded (dart:async/zone.dart:1209)
#19     _CustomZone.bindCallbackGuarded.<anonymous closure> (dart:async/zone.dart:1249)
#20     _rootRun (dart:async/zone.dart:1399)
#21     _CustomZone.run (dart:async/zone.dart:1301)
#22     _CustomZone.runGuarded (dart:async/zone.dart:1209)
#23     _CustomZone.bindCallbackGuarded.<anonymous closure> (dart:async/zone.dart:1249)
#24     _microtaskLoop (dart:async/schedule_microtask.dart:40)
#25     _startMicrotaskLoop (dart:async/schedule_microtask.dart:49)

2025-08-26 16:35:51.806595: 接收到数据: aa 00 01 81 00 00 49 de
2025-08-26 16:35:51.807591: 🔍 接收到串口数据: aa 00 01 81 00 00 49 de
2025-08-26 16:35:51.807591: 🔍 数据长度: 8 字节
2025-08-26 16:35:51.807591: 🔍 预定义命令列表:
2025-08-26 16:35:51.807591:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:35:51.807591:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:35:51.807591:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:35:51.807591:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:35:51.808588:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:35:51.808588:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:35:51.808588:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.808588:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:35:51.808588:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:35:51.808588:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:35:51.808588: ❌ 未识别的闸机命令 - 数据不匹配任何预定义命令
2025-08-26 16:35:51.808588: ❌ 接收数据: aa 00 01 81 00 00 49 de
2025-08-26 16:35:51.919221: 🔄 开始RFID轮询检查...
2025-08-26 16:35:51.919221: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:51.919221: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:51.919221: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:51.919221: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:51.920218: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:51.920218: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:51.923211: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:51.923211:   - 设备句柄: 2424588958144
2025-08-26 16:35:51.923211:   - FetchRecords返回值: 0
2025-08-26 16:35:51.924217:   - 报告数量: 5
2025-08-26 16:35:51.924217:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.924217:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:51.924217:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.924217:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.925201:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.925201:   - 数据长度: 8
2025-08-26 16:35:51.925201:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:51.925201:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:51.925201:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.925201:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:51.926199:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.926199:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.926199:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.926199:   - 数据长度: 8
2025-08-26 16:35:51.926199:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:51.926199:   - 提取的UID: E004015305F68508
2025-08-26 16:35:51.926199:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.927194:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:51.927194:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.927194:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.927194:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.927194:   - 数据长度: 8
2025-08-26 16:35:51.927194:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:51.927194:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:51.927194:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.928191:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:51.928191:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.928191:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.928191:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.928191:   - 数据长度: 8
2025-08-26 16:35:51.928191:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:51.928191:   - 提取的UID: E004015305F68508
2025-08-26 16:35:51.928191:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.929188:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:51.929188:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:51.929188:   - 设备类型: LSGControlCenter
2025-08-26 16:35:51.929188:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:51.929188:   - 数据长度: 8
2025-08-26 16:35:51.929188:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:51.929188:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:51.929188: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:51.930184:   - 发现标签数量: 5
2025-08-26 16:35:51.930184:   - 标签详情:
2025-08-26 16:35:51.930184:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:51.930184:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:51.930184:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:51.930184:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:51.930184:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:51.931181: RFID扫描: 发现 5 个标签
2025-08-26 16:35:51.931181: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:51.931181: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:51.931181: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.931181:   - UID: E004015304F3DD22
2025-08-26 16:35:51.931181:   - Data: E004015304F3DD22
2025-08-26 16:35:51.932182:   - EventType: 1
2025-08-26 16:35:51.932182:   - Direction: 0
2025-08-26 16:35:51.932182:   - Antenna: 1
2025-08-26 16:35:51.932182:   - TagFrequency: 0
2025-08-26 16:35:51.933175: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.933175:   - UID: E004015305F68508
2025-08-26 16:35:51.933175:   - Data: E004015305F68508
2025-08-26 16:35:51.933175:   - EventType: 1
2025-08-26 16:35:51.934173:   - Direction: 0
2025-08-26 16:35:51.934173:   - Antenna: 1
2025-08-26 16:35:51.934173:   - TagFrequency: 0
2025-08-26 16:35:51.934173: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.934173:   - UID: E004015304F3DD22
2025-08-26 16:35:51.935168:   - Data: E004015304F3DD22
2025-08-26 16:35:51.935168:   - EventType: 1
2025-08-26 16:35:51.935168:   - Direction: 0
2025-08-26 16:35:51.935168:   - Antenna: 1
2025-08-26 16:35:51.935168:   - TagFrequency: 0
2025-08-26 16:35:51.935168: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.935168:   - UID: E004015305F68508
2025-08-26 16:35:51.936165:   - Data: E004015305F68508
2025-08-26 16:35:51.936165:   - EventType: 1
2025-08-26 16:35:51.936165:   - Direction: 0
2025-08-26 16:35:51.936165:   - Antenna: 1
2025-08-26 16:35:51.936165:   - TagFrequency: 0
2025-08-26 16:35:51.936165: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:51.936165:   - UID: E004015304F3DD22
2025-08-26 16:35:51.936165:   - Data: E004015304F3DD22
2025-08-26 16:35:51.937161:   - EventType: 1
2025-08-26 16:35:51.937161:   - Direction: 0
2025-08-26 16:35:51.937161:   - Antenna: 1
2025-08-26 16:35:51.937161:   - TagFrequency: 0
2025-08-26 16:35:52.418565: 🔄 开始RFID轮询检查...
2025-08-26 16:35:52.418565: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:52.418565: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:52.418565: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:52.419563: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:52.419563: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:52.419563: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:52.421555: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:52.422552:   - 设备句柄: 2424588958144
2025-08-26 16:35:52.422552:   - FetchRecords返回值: 0
2025-08-26 16:35:52.422552:   - 报告数量: 5
2025-08-26 16:35:52.422552:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.422552:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:52.422552:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.423549:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.423549:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.423549:   - 数据长度: 8
2025-08-26 16:35:52.423549:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:52.423549:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:52.423549:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.423549:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:52.424545:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.424545:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.424545:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.424545:   - 数据长度: 8
2025-08-26 16:35:52.424545:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:52.424545:   - 提取的UID: E004015305F68508
2025-08-26 16:35:52.424545:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.425547:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:52.425547:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.426542:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.426542:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.427537:   - 数据长度: 8
2025-08-26 16:35:52.427537:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:52.427537:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:52.428532:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.428532:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:52.428532:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.428532:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.428532:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.428532:   - 数据长度: 8
2025-08-26 16:35:52.429535:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:52.429535:   - 提取的UID: E004015305F68508
2025-08-26 16:35:52.429535:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.429535:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:52.430526:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.430526:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.430526:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.430526:   - 数据长度: 8
2025-08-26 16:35:52.430526:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:52.430526:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:52.430526: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:52.431522:   - 发现标签数量: 5
2025-08-26 16:35:52.431522:   - 标签详情:
2025-08-26 16:35:52.431522:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:52.431522:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:52.431522:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:52.431522:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:52.432521:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:52.432521: RFID扫描: 发现 5 个标签
2025-08-26 16:35:52.432521: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:52.432521: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:52.432521: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.432521:   - UID: E004015304F3DD22
2025-08-26 16:35:52.432521:   - Data: E004015304F3DD22
2025-08-26 16:35:52.433516:   - EventType: 1
2025-08-26 16:35:52.433516:   - Direction: 0
2025-08-26 16:35:52.433516:   - Antenna: 1
2025-08-26 16:35:52.433516:   - TagFrequency: 0
2025-08-26 16:35:52.433516: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.433516:   - UID: E004015305F68508
2025-08-26 16:35:52.433516:   - Data: E004015305F68508
2025-08-26 16:35:52.433516:   - EventType: 1
2025-08-26 16:35:52.434512:   - Direction: 0
2025-08-26 16:35:52.434512:   - Antenna: 1
2025-08-26 16:35:52.434512:   - TagFrequency: 0
2025-08-26 16:35:52.434512: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.434512:   - UID: E004015304F3DD22
2025-08-26 16:35:52.434512:   - Data: E004015304F3DD22
2025-08-26 16:35:52.435509:   - EventType: 1
2025-08-26 16:35:52.435509:   - Direction: 0
2025-08-26 16:35:52.435509:   - Antenna: 1
2025-08-26 16:35:52.435509:   - TagFrequency: 0
2025-08-26 16:35:52.435509: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.435509:   - UID: E004015305F68508
2025-08-26 16:35:52.436505:   - Data: E004015305F68508
2025-08-26 16:35:52.436505:   - EventType: 1
2025-08-26 16:35:52.436505:   - Direction: 0
2025-08-26 16:35:52.436505:   - Antenna: 1
2025-08-26 16:35:52.436505:   - TagFrequency: 0
2025-08-26 16:35:52.436505: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.436505:   - UID: E004015304F3DD22
2025-08-26 16:35:52.436505:   - Data: E004015304F3DD22
2025-08-26 16:35:52.437502:   - EventType: 1
2025-08-26 16:35:52.437502:   - Direction: 0
2025-08-26 16:35:52.437502:   - Antenna: 1
2025-08-26 16:35:52.437502:   - TagFrequency: 0
2025-08-26 16:35:52.918906: 🔄 开始RFID轮询检查...
2025-08-26 16:35:52.919906: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:52.919906: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:52.919906: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:52.920900: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:52.920900: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:52.920900: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:52.921897: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:52.922893:   - 设备句柄: 2424588958144
2025-08-26 16:35:52.922893:   - FetchRecords返回值: 0
2025-08-26 16:35:52.922893:   - 报告数量: 5
2025-08-26 16:35:52.922893:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.922893:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:52.922893:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.923890:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.923890:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.923890:   - 数据长度: 8
2025-08-26 16:35:52.923890:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:52.923890:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:52.923890:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.923890:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:52.924886:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.924886:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.924886:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.924886:   - 数据长度: 8
2025-08-26 16:35:52.924886:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:52.924886:   - 提取的UID: E004015305F68508
2025-08-26 16:35:52.924886:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.924886:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:52.925883:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.925883:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.925883:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.925883:   - 数据长度: 8
2025-08-26 16:35:52.925883:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:52.925883:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:52.925883:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.926879:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:52.926879:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.926879:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.926879:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.926879:   - 数据长度: 8
2025-08-26 16:35:52.926879:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:52.926879:   - 提取的UID: E004015305F68508
2025-08-26 16:35:52.926879:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:52.927876:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:52.927876:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:52.927876:   - 设备类型: LSGControlCenter
2025-08-26 16:35:52.927876:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:52.927876:   - 数据长度: 8
2025-08-26 16:35:52.927876:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:52.927876:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:52.927876: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:52.928873:   - 发现标签数量: 5
2025-08-26 16:35:52.928873:   - 标签详情:
2025-08-26 16:35:52.928873:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:52.928873:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:52.928873:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:52.928873:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:52.928873:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:52.928873: RFID扫描: 发现 5 个标签
2025-08-26 16:35:52.929869: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:52.929869: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:52.929869: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.929869:   - UID: E004015304F3DD22
2025-08-26 16:35:52.929869:   - Data: E004015304F3DD22
2025-08-26 16:35:52.929869:   - EventType: 1
2025-08-26 16:35:52.929869:   - Direction: 0
2025-08-26 16:35:52.929869:   - Antenna: 1
2025-08-26 16:35:52.930866:   - TagFrequency: 0
2025-08-26 16:35:52.930866: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.930866:   - UID: E004015305F68508
2025-08-26 16:35:52.930866:   - Data: E004015305F68508
2025-08-26 16:35:52.930866:   - EventType: 1
2025-08-26 16:35:52.930866:   - Direction: 0
2025-08-26 16:35:52.930866:   - Antenna: 1
2025-08-26 16:35:52.930866:   - TagFrequency: 0
2025-08-26 16:35:52.931863: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.931863:   - UID: E004015304F3DD22
2025-08-26 16:35:52.931863:   - Data: E004015304F3DD22
2025-08-26 16:35:52.931863:   - EventType: 1
2025-08-26 16:35:52.931863:   - Direction: 0
2025-08-26 16:35:52.931863:   - Antenna: 1
2025-08-26 16:35:52.931863:   - TagFrequency: 0
2025-08-26 16:35:52.931863: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.932860:   - UID: E004015305F68508
2025-08-26 16:35:52.932860:   - Data: E004015305F68508
2025-08-26 16:35:52.932860:   - EventType: 1
2025-08-26 16:35:52.932860:   - Direction: 0
2025-08-26 16:35:52.932860:   - Antenna: 1
2025-08-26 16:35:52.932860:   - TagFrequency: 0
2025-08-26 16:35:52.932860: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:52.932860:   - UID: E004015304F3DD22
2025-08-26 16:35:52.932860:   - Data: E004015304F3DD22
2025-08-26 16:35:52.933856:   - EventType: 1
2025-08-26 16:35:52.933856:   - Direction: 0
2025-08-26 16:35:52.933856:   - Antenna: 1
2025-08-26 16:35:52.933856:   - TagFrequency: 0
2025-08-26 16:35:53.419815: 🔄 开始RFID轮询检查...
2025-08-26 16:35:53.419815: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:53.419815: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:53.419815: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:53.420813: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:53.420813: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:53.420813: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:53.423802: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:53.423802:   - 设备句柄: 2424588958144
2025-08-26 16:35:53.423802:   - FetchRecords返回值: 0
2025-08-26 16:35:53.423802:   - 报告数量: 5
2025-08-26 16:35:53.423802:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.423802:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:53.424799:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.424799:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.424799:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.424799:   - 数据长度: 8
2025-08-26 16:35:53.424799:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:53.424799:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:53.424799:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.425796:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:53.425796:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.425796:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.425796:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.425796:   - 数据长度: 8
2025-08-26 16:35:53.425796:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:53.425796:   - 提取的UID: E004015305F68508
2025-08-26 16:35:53.425796:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.426792:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:53.426792:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.426792:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.426792:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.426792:   - 数据长度: 8
2025-08-26 16:35:53.426792:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:53.426792:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:53.426792:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.427788:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:53.427788:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.427788:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.427788:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.427788:   - 数据长度: 8
2025-08-26 16:35:53.427788:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:53.427788:   - 提取的UID: E004015305F68508
2025-08-26 16:35:53.428785:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.428785:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:53.428785:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.428785:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.428785:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.428785:   - 数据长度: 8
2025-08-26 16:35:53.428785:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:53.429787:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:53.429787: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:53.429787:   - 发现标签数量: 5
2025-08-26 16:35:53.429787:   - 标签详情:
2025-08-26 16:35:53.430781:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:53.430781:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:53.430781:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:53.430781:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:53.431777:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:53.431777: RFID扫描: 发现 5 个标签
2025-08-26 16:35:53.431777: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:53.431777: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:53.431777: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.432773:   - UID: E004015304F3DD22
2025-08-26 16:35:53.432773:   - Data: E004015304F3DD22
2025-08-26 16:35:53.432773:   - EventType: 1
2025-08-26 16:35:53.432773:   - Direction: 0
2025-08-26 16:35:53.432773:   - Antenna: 1
2025-08-26 16:35:53.432773:   - TagFrequency: 0
2025-08-26 16:35:53.432773: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.433769:   - UID: E004015305F68508
2025-08-26 16:35:53.433769:   - Data: E004015305F68508
2025-08-26 16:35:53.433769:   - EventType: 1
2025-08-26 16:35:53.433769:   - Direction: 0
2025-08-26 16:35:53.433769:   - Antenna: 1
2025-08-26 16:35:53.433769:   - TagFrequency: 0
2025-08-26 16:35:53.433769: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.433769:   - UID: E004015304F3DD22
2025-08-26 16:35:53.434765:   - Data: E004015304F3DD22
2025-08-26 16:35:53.434765:   - EventType: 1
2025-08-26 16:35:53.434765:   - Direction: 0
2025-08-26 16:35:53.434765:   - Antenna: 1
2025-08-26 16:35:53.434765:   - TagFrequency: 0
2025-08-26 16:35:53.434765: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.434765:   - UID: E004015305F68508
2025-08-26 16:35:53.434765:   - Data: E004015305F68508
2025-08-26 16:35:53.435762:   - EventType: 1
2025-08-26 16:35:53.435762:   - Direction: 0
2025-08-26 16:35:53.435762:   - Antenna: 1
2025-08-26 16:35:53.435762:   - TagFrequency: 0
2025-08-26 16:35:53.435762: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.435762:   - UID: E004015304F3DD22
2025-08-26 16:35:53.435762:   - Data: E004015304F3DD22
2025-08-26 16:35:53.435762:   - EventType: 1
2025-08-26 16:35:53.436759:   - Direction: 0
2025-08-26 16:35:53.436759:   - Antenna: 1
2025-08-26 16:35:53.436759:   - TagFrequency: 0
2025-08-26 16:35:53.919159: 🔄 开始RFID轮询检查...
2025-08-26 16:35:53.919159: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:53.919159: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:53.919159: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:53.919159: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:53.920157: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:53.920157: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:53.922149: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:53.923146:   - 设备句柄: 2424588958144
2025-08-26 16:35:53.923146:   - FetchRecords返回值: 0
2025-08-26 16:35:53.924143:   - 报告数量: 5
2025-08-26 16:35:53.924143:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.925148:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:53.925148:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.925148:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.926137:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.926137:   - 数据长度: 8
2025-08-26 16:35:53.926137:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:53.926137:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:53.927134:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.927134:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:53.927134:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.927134:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.927134:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.928131:   - 数据长度: 8
2025-08-26 16:35:53.928131:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:53.928131:   - 提取的UID: E004015305F68508
2025-08-26 16:35:53.928131:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.928131:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:53.928131:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.928131:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.929127:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.929127:   - 数据长度: 8
2025-08-26 16:35:53.929127:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:53.929127:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:53.929127:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.929127:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:53.930123:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.930123:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.930123:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.930123:   - 数据长度: 8
2025-08-26 16:35:53.930123:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:53.930123:   - 提取的UID: E004015305F68508
2025-08-26 16:35:53.930123:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:53.931120:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:53.931120:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:53.931120:   - 设备类型: LSGControlCenter
2025-08-26 16:35:53.931120:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:53.931120:   - 数据长度: 8
2025-08-26 16:35:53.931120:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:53.931120:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:53.932116: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:53.932116:   - 发现标签数量: 5
2025-08-26 16:35:53.932116:   - 标签详情:
2025-08-26 16:35:53.932116:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:53.932116:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:53.932116:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:53.932116:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:53.933113:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:53.933113: RFID扫描: 发现 5 个标签
2025-08-26 16:35:53.933113: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:53.933113: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:53.933113: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.933113:   - UID: E004015304F3DD22
2025-08-26 16:35:53.934113:   - Data: E004015304F3DD22
2025-08-26 16:35:53.934113:   - EventType: 1
2025-08-26 16:35:53.934113:   - Direction: 0
2025-08-26 16:35:53.934113:   - Antenna: 1
2025-08-26 16:35:53.934113:   - TagFrequency: 0
2025-08-26 16:35:53.934113: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.934113:   - UID: E004015305F68508
2025-08-26 16:35:53.935106:   - Data: E004015305F68508
2025-08-26 16:35:53.935106:   - EventType: 1
2025-08-26 16:35:53.935106:   - Direction: 0
2025-08-26 16:35:53.935106:   - Antenna: 1
2025-08-26 16:35:53.935106:   - TagFrequency: 0
2025-08-26 16:35:53.935106: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.935106:   - UID: E004015304F3DD22
2025-08-26 16:35:53.935106:   - Data: E004015304F3DD22
2025-08-26 16:35:53.936103:   - EventType: 1
2025-08-26 16:35:53.936103:   - Direction: 0
2025-08-26 16:35:53.936103:   - Antenna: 1
2025-08-26 16:35:53.936103:   - TagFrequency: 0
2025-08-26 16:35:53.936103: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.936103:   - UID: E004015305F68508
2025-08-26 16:35:53.936103:   - Data: E004015305F68508
2025-08-26 16:35:53.937100:   - EventType: 1
2025-08-26 16:35:53.937100:   - Direction: 0
2025-08-26 16:35:53.937100:   - Antenna: 1
2025-08-26 16:35:53.937100:   - TagFrequency: 0
2025-08-26 16:35:53.937100: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:53.937100:   - UID: E004015304F3DD22
2025-08-26 16:35:53.937100:   - Data: E004015304F3DD22
2025-08-26 16:35:53.937100:   - EventType: 1
2025-08-26 16:35:53.938097:   - Direction: 0
2025-08-26 16:35:53.938097:   - Antenna: 1
2025-08-26 16:35:53.938097:   - TagFrequency: 0
2025-08-26 16:35:54.418509: 🔄 开始RFID轮询检查...
2025-08-26 16:35:54.418509: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:54.419501: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:54.419501: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:54.419501: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:54.419501: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:54.419501: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:54.422490: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:54.422490:   - 设备句柄: 2424588958144
2025-08-26 16:35:54.422490:   - FetchRecords返回值: 0
2025-08-26 16:35:54.422490:   - 报告数量: 5
2025-08-26 16:35:54.422490:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.422490:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:54.423487:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.423487:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.423487:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.423487:   - 数据长度: 8
2025-08-26 16:35:54.423487:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:54.423487:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:54.423487:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.424484:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:54.424484:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.424484:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.424484:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.424484:   - 数据长度: 8
2025-08-26 16:35:54.424484:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:54.424484:   - 提取的UID: E004015305F68508
2025-08-26 16:35:54.424484:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.425480:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:54.425480:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.425480:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.425480:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.425480:   - 数据长度: 8
2025-08-26 16:35:54.425480:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:54.425480:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:54.426477:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.426477:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:54.426477:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.426477:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.426477:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.426477:   - 数据长度: 8
2025-08-26 16:35:54.426477:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:54.426477:   - 提取的UID: E004015305F68508
2025-08-26 16:35:54.427474:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.427474:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:54.427474:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.427474:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.427474:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.427474:   - 数据长度: 8
2025-08-26 16:35:54.427474:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:54.427474:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:54.428470: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:54.428470:   - 发现标签数量: 5
2025-08-26 16:35:54.428470:   - 标签详情:
2025-08-26 16:35:54.428470:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:54.428470:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:54.428470:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:54.428470:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:54.428470:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:54.429467: RFID扫描: 发现 5 个标签
2025-08-26 16:35:54.429467: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:54.429467: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:54.429467: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.429467:   - UID: E004015304F3DD22
2025-08-26 16:35:54.429467:   - Data: E004015304F3DD22
2025-08-26 16:35:54.429467:   - EventType: 1
2025-08-26 16:35:54.429467:   - Direction: 0
2025-08-26 16:35:54.430464:   - Antenna: 1
2025-08-26 16:35:54.430464:   - TagFrequency: 0
2025-08-26 16:35:54.430464: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.430464:   - UID: E004015305F68508
2025-08-26 16:35:54.430464:   - Data: E004015305F68508
2025-08-26 16:35:54.430464:   - EventType: 1
2025-08-26 16:35:54.430464:   - Direction: 0
2025-08-26 16:35:54.430464:   - Antenna: 1
2025-08-26 16:35:54.430464:   - TagFrequency: 0
2025-08-26 16:35:54.431460: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.431460:   - UID: E004015304F3DD22
2025-08-26 16:35:54.431460:   - Data: E004015304F3DD22
2025-08-26 16:35:54.431460:   - EventType: 1
2025-08-26 16:35:54.431460:   - Direction: 0
2025-08-26 16:35:54.431460:   - Antenna: 1
2025-08-26 16:35:54.431460:   - TagFrequency: 0
2025-08-26 16:35:54.431460: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.432457:   - UID: E004015305F68508
2025-08-26 16:35:54.432457:   - Data: E004015305F68508
2025-08-26 16:35:54.432457:   - EventType: 1
2025-08-26 16:35:54.432457:   - Direction: 0
2025-08-26 16:35:54.432457:   - Antenna: 1
2025-08-26 16:35:54.432457:   - TagFrequency: 0
2025-08-26 16:35:54.432457: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.432457:   - UID: E004015304F3DD22
2025-08-26 16:35:54.433454:   - Data: E004015304F3DD22
2025-08-26 16:35:54.433454:   - EventType: 1
2025-08-26 16:35:54.433454:   - Direction: 0
2025-08-26 16:35:54.433454:   - Antenna: 1
2025-08-26 16:35:54.433454:   - TagFrequency: 0
2025-08-26 16:35:54.776318: 模拟出馆完成，状态重置为idle
2025-08-26 16:35:54.776318: 闸机状态变更: GateState.exitOver -> GateState.idle
2025-08-26 16:35:54.777314: 闸机状态更新: GateState.exitOver -> GateState.idle
2025-08-26 16:35:54.777314: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:35:54.777314: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:35:54.777314: 闸机状态变更: GateState.idle
2025-08-26 16:35:54.777314: 🎨 处理状态变更UI: idle
2025-08-26 16:35:54.777314: 页面状态变更: SilencePageState.welcome
2025-08-26 16:35:54.919842: 🔄 开始RFID轮询检查...
2025-08-26 16:35:54.919842: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:54.919842: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:54.919842: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:54.919842: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:54.919842: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:54.920838: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:54.922832: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:54.922832:   - 设备句柄: 2424588958144
2025-08-26 16:35:54.922832:   - FetchRecords返回值: 0
2025-08-26 16:35:54.922832:   - 报告数量: 5
2025-08-26 16:35:54.922832:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.922832:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:54.923828:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.923828:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.923828:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.923828:   - 数据长度: 8
2025-08-26 16:35:54.923828:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:54.923828:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:54.923828:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.923828:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:54.924824:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.924824:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.924824:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.924824:   - 数据长度: 8
2025-08-26 16:35:54.924824:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:54.924824:   - 提取的UID: E004015305F68508
2025-08-26 16:35:54.924824:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.925821:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:54.925821:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.925821:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.925821:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.925821:   - 数据长度: 8
2025-08-26 16:35:54.925821:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:54.925821:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:54.925821:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.926818:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:54.926818:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.926818:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.926818:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.926818:   - 数据长度: 8
2025-08-26 16:35:54.926818:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:54.926818:   - 提取的UID: E004015305F68508
2025-08-26 16:35:54.927823:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:54.927823:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:54.927823:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:54.927823:   - 设备类型: LSGControlCenter
2025-08-26 16:35:54.928815:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:54.928815:   - 数据长度: 8
2025-08-26 16:35:54.928815:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:54.928815:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:54.929809: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:54.929809:   - 发现标签数量: 5
2025-08-26 16:35:54.929809:   - 标签详情:
2025-08-26 16:35:54.929809:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:54.929809:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:54.930805:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:54.930805:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:54.930805:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:54.930805: RFID扫描: 发现 5 个标签
2025-08-26 16:35:54.930805: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:54.930805: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:54.930805: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.931801:   - UID: E004015304F3DD22
2025-08-26 16:35:54.931801:   - Data: E004015304F3DD22
2025-08-26 16:35:54.931801:   - EventType: 1
2025-08-26 16:35:54.931801:   - Direction: 0
2025-08-26 16:35:54.931801:   - Antenna: 1
2025-08-26 16:35:54.931801:   - TagFrequency: 0
2025-08-26 16:35:54.931801: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.931801:   - UID: E004015305F68508
2025-08-26 16:35:54.932798:   - Data: E004015305F68508
2025-08-26 16:35:54.932798:   - EventType: 1
2025-08-26 16:35:54.932798:   - Direction: 0
2025-08-26 16:35:54.932798:   - Antenna: 1
2025-08-26 16:35:54.932798:   - TagFrequency: 0
2025-08-26 16:35:54.932798: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.933795:   - UID: E004015304F3DD22
2025-08-26 16:35:54.933795:   - Data: E004015304F3DD22
2025-08-26 16:35:54.933795:   - EventType: 1
2025-08-26 16:35:54.933795:   - Direction: 0
2025-08-26 16:35:54.933795:   - Antenna: 1
2025-08-26 16:35:54.933795:   - TagFrequency: 0
2025-08-26 16:35:54.933795: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.933795:   - UID: E004015305F68508
2025-08-26 16:35:54.934792:   - Data: E004015305F68508
2025-08-26 16:35:54.934792:   - EventType: 1
2025-08-26 16:35:54.934792:   - Direction: 0
2025-08-26 16:35:54.934792:   - Antenna: 1
2025-08-26 16:35:54.934792:   - TagFrequency: 0
2025-08-26 16:35:54.934792: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:54.934792:   - UID: E004015304F3DD22
2025-08-26 16:35:54.934792:   - Data: E004015304F3DD22
2025-08-26 16:35:54.934792:   - EventType: 1
2025-08-26 16:35:54.935788:   - Direction: 0
2025-08-26 16:35:54.935788:   - Antenna: 1
2025-08-26 16:35:54.935788:   - TagFrequency: 0
2025-08-26 16:35:55.419185: 🔄 开始RFID轮询检查...
2025-08-26 16:35:55.419185: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:55.419185: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:55.419185: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:55.420182: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:55.420182: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:55.420182: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:55.423180: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:55.424179:   - 设备句柄: 2424588958144
2025-08-26 16:35:55.424179:   - FetchRecords返回值: 0
2025-08-26 16:35:55.424179:   - 报告数量: 5
2025-08-26 16:35:55.424179:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.425167:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:55.425167:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.425167:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.425167:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.425167:   - 数据长度: 8
2025-08-26 16:35:55.426167:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:55.426167:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:55.426167:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.426167:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:55.426167:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.426167:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.427159:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.427159:   - 数据长度: 8
2025-08-26 16:35:55.427159:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:55.427159:   - 提取的UID: E004015305F68508
2025-08-26 16:35:55.427159:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.427159:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:55.427159:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.428156:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.428156:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.428156:   - 数据长度: 8
2025-08-26 16:35:55.428156:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:55.428156:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:55.428156:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.428156:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:55.429152:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.429152:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.429152:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.429152:   - 数据长度: 8
2025-08-26 16:35:55.429152:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:55.429152:   - 提取的UID: E004015305F68508
2025-08-26 16:35:55.429152:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.430150:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:55.430150:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.430150:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.430150:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.430150:   - 数据长度: 8
2025-08-26 16:35:55.431159:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:55.431159:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:55.431159: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:55.431159:   - 发现标签数量: 5
2025-08-26 16:35:55.431159:   - 标签详情:
2025-08-26 16:35:55.431159:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:55.432142:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:55.432142:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:55.432142:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:55.432142:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:55.432142: RFID扫描: 发现 5 个标签
2025-08-26 16:35:55.432142: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:55.432142: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:55.432142: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.433139:   - UID: E004015304F3DD22
2025-08-26 16:35:55.433139:   - Data: E004015304F3DD22
2025-08-26 16:35:55.433139:   - EventType: 1
2025-08-26 16:35:55.433139:   - Direction: 0
2025-08-26 16:35:55.433139:   - Antenna: 1
2025-08-26 16:35:55.433139:   - TagFrequency: 0
2025-08-26 16:35:55.433139: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.433139:   - UID: E004015305F68508
2025-08-26 16:35:55.434136:   - Data: E004015305F68508
2025-08-26 16:35:55.434136:   - EventType: 1
2025-08-26 16:35:55.434136:   - Direction: 0
2025-08-26 16:35:55.434136:   - Antenna: 1
2025-08-26 16:35:55.434136:   - TagFrequency: 0
2025-08-26 16:35:55.434136: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.434136:   - UID: E004015304F3DD22
2025-08-26 16:35:55.434136:   - Data: E004015304F3DD22
2025-08-26 16:35:55.435132:   - EventType: 1
2025-08-26 16:35:55.435132:   - Direction: 0
2025-08-26 16:35:55.435132:   - Antenna: 1
2025-08-26 16:35:55.435132:   - TagFrequency: 0
2025-08-26 16:35:55.435132: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.435132:   - UID: E004015305F68508
2025-08-26 16:35:55.435132:   - Data: E004015305F68508
2025-08-26 16:35:55.435132:   - EventType: 1
2025-08-26 16:35:55.436129:   - Direction: 0
2025-08-26 16:35:55.436129:   - Antenna: 1
2025-08-26 16:35:55.436129:   - TagFrequency: 0
2025-08-26 16:35:55.436129: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.436129:   - UID: E004015304F3DD22
2025-08-26 16:35:55.436129:   - Data: E004015304F3DD22
2025-08-26 16:35:55.436129:   - EventType: 1
2025-08-26 16:35:55.436129:   - Direction: 0
2025-08-26 16:35:55.437126:   - Antenna: 1
2025-08-26 16:35:55.437126:   - TagFrequency: 0
2025-08-26 16:35:55.919044: 🔄 开始RFID轮询检查...
2025-08-26 16:35:55.919044: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:55.919044: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:55.920041: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:55.920041: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:55.920041: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:55.920041: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:55.923031: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:55.923031:   - 设备句柄: 2424588958144
2025-08-26 16:35:55.924034:   - FetchRecords返回值: 0
2025-08-26 16:35:55.924034:   - 报告数量: 5
2025-08-26 16:35:55.924034:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.924034:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:55.924034:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.925026:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.925026:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.925026:   - 数据长度: 8
2025-08-26 16:35:55.925026:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:55.925026:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:55.925026:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.926022:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:55.926022:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.926022:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.926022:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.926022:   - 数据长度: 8
2025-08-26 16:35:55.926022:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:55.926022:   - 提取的UID: E004015305F68508
2025-08-26 16:35:55.927018:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.927018:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:55.927018:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.927018:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.927018:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.927018:   - 数据长度: 8
2025-08-26 16:35:55.927018:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:55.927018:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:55.928015:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.928015:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:55.928015:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.928015:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.928015:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.928015:   - 数据长度: 8
2025-08-26 16:35:55.928015:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:55.929011:   - 提取的UID: E004015305F68508
2025-08-26 16:35:55.929011:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:55.929011:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:55.929011:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:55.929011:   - 设备类型: LSGControlCenter
2025-08-26 16:35:55.929011:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:55.929011:   - 数据长度: 8
2025-08-26 16:35:55.929011:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:55.930008:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:55.930008: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:55.930008:   - 发现标签数量: 5
2025-08-26 16:35:55.930008:   - 标签详情:
2025-08-26 16:35:55.930008:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:55.930008:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:55.930008:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:55.930008:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:55.931005:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:55.931005: RFID扫描: 发现 5 个标签
2025-08-26 16:35:55.931005: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:55.931005: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:55.931005: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.931005:   - UID: E004015304F3DD22
2025-08-26 16:35:55.931005:   - Data: E004015304F3DD22
2025-08-26 16:35:55.931005:   - EventType: 1
2025-08-26 16:35:55.932003:   - Direction: 0
2025-08-26 16:35:55.932003:   - Antenna: 1
2025-08-26 16:35:55.932003:   - TagFrequency: 0
2025-08-26 16:35:55.933: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.933:   - UID: E004015305F68508
2025-08-26 16:35:55.933:   - Data: E004015305F68508
2025-08-26 16:35:55.933:   - EventType: 1
2025-08-26 16:35:55.933995:   - Direction: 0
2025-08-26 16:35:55.933995:   - Antenna: 1
2025-08-26 16:35:55.933995:   - TagFrequency: 0
2025-08-26 16:35:55.933995: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.933995:   - UID: E004015304F3DD22
2025-08-26 16:35:55.934993:   - Data: E004015304F3DD22
2025-08-26 16:35:55.934993:   - EventType: 1
2025-08-26 16:35:55.934993:   - Direction: 0
2025-08-26 16:35:55.934993:   - Antenna: 1
2025-08-26 16:35:55.934993:   - TagFrequency: 0
2025-08-26 16:35:55.934993: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.935988:   - UID: E004015305F68508
2025-08-26 16:35:55.935988:   - Data: E004015305F68508
2025-08-26 16:35:55.935988:   - EventType: 1
2025-08-26 16:35:55.935988:   - Direction: 0
2025-08-26 16:35:55.935988:   - Antenna: 1
2025-08-26 16:35:55.935988:   - TagFrequency: 0
2025-08-26 16:35:55.935988: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:55.936985:   - UID: E004015304F3DD22
2025-08-26 16:35:55.936985:   - Data: E004015304F3DD22
2025-08-26 16:35:55.936985:   - EventType: 1
2025-08-26 16:35:55.936985:   - Direction: 0
2025-08-26 16:35:55.936985:   - Antenna: 1
2025-08-26 16:35:55.936985:   - TagFrequency: 0
2025-08-26 16:35:56.419907: 🔄 开始RFID轮询检查...
2025-08-26 16:35:56.419907: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:56.419907: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:56.419907: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:56.419907: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:56.420904: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:56.420904: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:56.422897: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:56.423894:   - 设备句柄: 2424588958144
2025-08-26 16:35:56.423894:   - FetchRecords返回值: 0
2025-08-26 16:35:56.423894:   - 报告数量: 5
2025-08-26 16:35:56.423894:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.423894:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:56.423894:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.423894:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.424891:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.424891:   - 数据长度: 8
2025-08-26 16:35:56.424891:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:56.424891:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:56.424891:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.424891:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:56.425897:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.425897:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.425897:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.425897:   - 数据长度: 8
2025-08-26 16:35:56.426887:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:56.426887:   - 提取的UID: E004015305F68508
2025-08-26 16:35:56.426887:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.426887:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:56.427882:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.427882:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.427882:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.427882:   - 数据长度: 8
2025-08-26 16:35:56.427882:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:56.428878:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:56.428878:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.428878:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:56.428878:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.428878:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.428878:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.428878:   - 数据长度: 8
2025-08-26 16:35:56.429874:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:56.429874:   - 提取的UID: E004015305F68508
2025-08-26 16:35:56.429874:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.429874:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:56.429874:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.429874:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.429874:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.429874:   - 数据长度: 8
2025-08-26 16:35:56.430871:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:56.430871:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:56.430871: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:56.430871:   - 发现标签数量: 5
2025-08-26 16:35:56.430871:   - 标签详情:
2025-08-26 16:35:56.430871:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:56.430871:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:56.430871:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:56.431867:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:56.431867:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:56.431867: RFID扫描: 发现 5 个标签
2025-08-26 16:35:56.431867: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:56.431867: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:56.431867: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.431867:   - UID: E004015304F3DD22
2025-08-26 16:35:56.431867:   - Data: E004015304F3DD22
2025-08-26 16:35:56.432864:   - EventType: 1
2025-08-26 16:35:56.432864:   - Direction: 0
2025-08-26 16:35:56.432864:   - Antenna: 1
2025-08-26 16:35:56.432864:   - TagFrequency: 0
2025-08-26 16:35:56.432864: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.432864:   - UID: E004015305F68508
2025-08-26 16:35:56.432864:   - Data: E004015305F68508
2025-08-26 16:35:56.432864:   - EventType: 1
2025-08-26 16:35:56.433862:   - Direction: 0
2025-08-26 16:35:56.433862:   - Antenna: 1
2025-08-26 16:35:56.433862:   - TagFrequency: 0
2025-08-26 16:35:56.433862: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.433862:   - UID: E004015304F3DD22
2025-08-26 16:35:56.433862:   - Data: E004015304F3DD22
2025-08-26 16:35:56.433862:   - EventType: 1
2025-08-26 16:35:56.433862:   - Direction: 0
2025-08-26 16:35:56.434857:   - Antenna: 1
2025-08-26 16:35:56.434857:   - TagFrequency: 0
2025-08-26 16:35:56.434857: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.434857:   - UID: E004015305F68508
2025-08-26 16:35:56.434857:   - Data: E004015305F68508
2025-08-26 16:35:56.434857:   - EventType: 1
2025-08-26 16:35:56.434857:   - Direction: 0
2025-08-26 16:35:56.435854:   - Antenna: 1
2025-08-26 16:35:56.435854:   - TagFrequency: 0
2025-08-26 16:35:56.435854: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.435854:   - UID: E004015304F3DD22
2025-08-26 16:35:56.435854:   - Data: E004015304F3DD22
2025-08-26 16:35:56.435854:   - EventType: 1
2025-08-26 16:35:56.435854:   - Direction: 0
2025-08-26 16:35:56.435854:   - Antenna: 1
2025-08-26 16:35:56.436851:   - TagFrequency: 0
2025-08-26 16:35:56.919252: 🔄 开始RFID轮询检查...
2025-08-26 16:35:56.920251: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:56.921245: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:56.921245: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:56.921245: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:56.922242: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:56.922242: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:56.923239: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:56.923239:   - 设备句柄: 2424588958144
2025-08-26 16:35:56.923239:   - FetchRecords返回值: 0
2025-08-26 16:35:56.924238:   - 报告数量: 5
2025-08-26 16:35:56.924238:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.924238:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:56.924238:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.924238:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.924238:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.924238:   - 数据长度: 8
2025-08-26 16:35:56.925231:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:56.925231:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:56.925231:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.925231:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:56.925231:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.925231:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.926228:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.926228:   - 数据长度: 8
2025-08-26 16:35:56.926228:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:56.926228:   - 提取的UID: E004015305F68508
2025-08-26 16:35:56.926228:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.926228:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:56.926228:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.927225:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.927225:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.927225:   - 数据长度: 8
2025-08-26 16:35:56.927225:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:56.927225:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:56.927225:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.927225:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:56.928221:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.928221:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.928221:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.928221:   - 数据长度: 8
2025-08-26 16:35:56.928221:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:56.928221:   - 提取的UID: E004015305F68508
2025-08-26 16:35:56.929218:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:56.929218:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:56.929218:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:56.929218:   - 设备类型: LSGControlCenter
2025-08-26 16:35:56.929218:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:56.929218:   - 数据长度: 8
2025-08-26 16:35:56.929218:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:56.930220:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:56.930220: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:56.930220:   - 发现标签数量: 5
2025-08-26 16:35:56.930220:   - 标签详情:
2025-08-26 16:35:56.930220:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:56.930220:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:56.931211:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:56.931211:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:56.931211:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:56.931211: RFID扫描: 发现 5 个标签
2025-08-26 16:35:56.931211: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:56.931211: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:56.931211: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.931211:   - UID: E004015304F3DD22
2025-08-26 16:35:56.932208:   - Data: E004015304F3DD22
2025-08-26 16:35:56.932208:   - EventType: 1
2025-08-26 16:35:56.932208:   - Direction: 0
2025-08-26 16:35:56.932208:   - Antenna: 1
2025-08-26 16:35:56.932208:   - TagFrequency: 0
2025-08-26 16:35:56.932208: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.932208:   - UID: E004015305F68508
2025-08-26 16:35:56.932208:   - Data: E004015305F68508
2025-08-26 16:35:56.933205:   - EventType: 1
2025-08-26 16:35:56.933205:   - Direction: 0
2025-08-26 16:35:56.933205:   - Antenna: 1
2025-08-26 16:35:56.933205:   - TagFrequency: 0
2025-08-26 16:35:56.933205: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.933205:   - UID: E004015304F3DD22
2025-08-26 16:35:56.933205:   - Data: E004015304F3DD22
2025-08-26 16:35:56.933205:   - EventType: 1
2025-08-26 16:35:56.934201:   - Direction: 0
2025-08-26 16:35:56.934201:   - Antenna: 1
2025-08-26 16:35:56.934201:   - TagFrequency: 0
2025-08-26 16:35:56.934201: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.934201:   - UID: E004015305F68508
2025-08-26 16:35:56.934201:   - Data: E004015305F68508
2025-08-26 16:35:56.934201:   - EventType: 1
2025-08-26 16:35:56.934201:   - Direction: 0
2025-08-26 16:35:56.935198:   - Antenna: 1
2025-08-26 16:35:56.935198:   - TagFrequency: 0
2025-08-26 16:35:56.935198: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:56.935198:   - UID: E004015304F3DD22
2025-08-26 16:35:56.935198:   - Data: E004015304F3DD22
2025-08-26 16:35:56.935198:   - EventType: 1
2025-08-26 16:35:56.935198:   - Direction: 0
2025-08-26 16:35:56.935198:   - Antenna: 1
2025-08-26 16:35:56.936195:   - TagFrequency: 0
2025-08-26 16:35:57.418595: 🔄 开始RFID轮询检查...
2025-08-26 16:35:57.418595: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:57.418595: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:57.418595: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:57.418595: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:57.419592: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:57.419592: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:57.421585: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:57.422595:   - 设备句柄: 2424588958144
2025-08-26 16:35:57.422595:   - FetchRecords返回值: 0
2025-08-26 16:35:57.422595:   - 报告数量: 5
2025-08-26 16:35:57.423579:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.423579:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:57.423579:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.423579:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.423579:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.423579:   - 数据长度: 8
2025-08-26 16:35:57.423579:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:57.424576:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:57.424576:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.424576:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:57.424576:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.424576:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.424576:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.424576:   - 数据长度: 8
2025-08-26 16:35:57.425572:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:57.425572:   - 提取的UID: E004015305F68508
2025-08-26 16:35:57.425572:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.425572:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:57.425572:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.425572:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.425572:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.425572:   - 数据长度: 8
2025-08-26 16:35:57.426569:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:57.426569:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:57.426569:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.426569:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:57.426569:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.426569:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.426569:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.426569:   - 数据长度: 8
2025-08-26 16:35:57.427566:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:57.427566:   - 提取的UID: E004015305F68508
2025-08-26 16:35:57.427566:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.427566:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:57.427566:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.427566:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.427566:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.427566:   - 数据长度: 8
2025-08-26 16:35:57.428562:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:57.428562:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:57.428562: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:57.428562:   - 发现标签数量: 5
2025-08-26 16:35:57.428562:   - 标签详情:
2025-08-26 16:35:57.428562:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:57.428562:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:57.429560:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:57.429560:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:57.429560:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:57.429560: RFID扫描: 发现 5 个标签
2025-08-26 16:35:57.429560: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:57.430560: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:57.430560: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.430560:   - UID: E004015304F3DD22
2025-08-26 16:35:57.431555:   - Data: E004015304F3DD22
2025-08-26 16:35:57.431555:   - EventType: 1
2025-08-26 16:35:57.431555:   - Direction: 0
2025-08-26 16:35:57.431555:   - Antenna: 1
2025-08-26 16:35:57.431555:   - TagFrequency: 0
2025-08-26 16:35:57.432550: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.432550:   - UID: E004015305F68508
2025-08-26 16:35:57.432550:   - Data: E004015305F68508
2025-08-26 16:35:57.432550:   - EventType: 1
2025-08-26 16:35:57.432550:   - Direction: 0
2025-08-26 16:35:57.433547:   - Antenna: 1
2025-08-26 16:35:57.433547:   - TagFrequency: 0
2025-08-26 16:35:57.433547: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.433547:   - UID: E004015304F3DD22
2025-08-26 16:35:57.433547:   - Data: E004015304F3DD22
2025-08-26 16:35:57.433547:   - EventType: 1
2025-08-26 16:35:57.433547:   - Direction: 0
2025-08-26 16:35:57.433547:   - Antenna: 1
2025-08-26 16:35:57.434542:   - TagFrequency: 0
2025-08-26 16:35:57.434542: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.434542:   - UID: E004015305F68508
2025-08-26 16:35:57.434542:   - Data: E004015305F68508
2025-08-26 16:35:57.434542:   - EventType: 1
2025-08-26 16:35:57.434542:   - Direction: 0
2025-08-26 16:35:57.434542:   - Antenna: 1
2025-08-26 16:35:57.435539:   - TagFrequency: 0
2025-08-26 16:35:57.435539: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.435539:   - UID: E004015304F3DD22
2025-08-26 16:35:57.435539:   - Data: E004015304F3DD22
2025-08-26 16:35:57.435539:   - EventType: 1
2025-08-26 16:35:57.435539:   - Direction: 0
2025-08-26 16:35:57.435539:   - Antenna: 1
2025-08-26 16:35:57.435539:   - TagFrequency: 0
2025-08-26 16:35:57.918937: 🔄 开始RFID轮询检查...
2025-08-26 16:35:57.918937: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:57.918937: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:57.918937: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:57.918937: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:57.918937: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:57.919933: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:57.921926: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:57.922923:   - 设备句柄: 2424588958144
2025-08-26 16:35:57.922923:   - FetchRecords返回值: 0
2025-08-26 16:35:57.922923:   - 报告数量: 5
2025-08-26 16:35:57.922923:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.922923:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:57.922923:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.922923:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.923920:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.923920:   - 数据长度: 8
2025-08-26 16:35:57.923920:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:57.923920:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:57.923920:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.923920:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:57.923920:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.924916:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.924916:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.924916:   - 数据长度: 8
2025-08-26 16:35:57.924916:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:57.924916:   - 提取的UID: E004015305F68508
2025-08-26 16:35:57.924916:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.924916:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:57.924916:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.925913:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.925913:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.925913:   - 数据长度: 8
2025-08-26 16:35:57.925913:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:57.925913:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:57.925913:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.925913:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:57.925913:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.926910:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.926910:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.926910:   - 数据长度: 8
2025-08-26 16:35:57.926910:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:57.926910:   - 提取的UID: E004015305F68508
2025-08-26 16:35:57.926910:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:57.926910:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:57.926910:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:57.927906:   - 设备类型: LSGControlCenter
2025-08-26 16:35:57.927906:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:57.927906:   - 数据长度: 8
2025-08-26 16:35:57.927906:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:57.927906:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:57.927906: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:57.927906:   - 发现标签数量: 5
2025-08-26 16:35:57.927906:   - 标签详情:
2025-08-26 16:35:57.928903:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:57.928903:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:57.928903:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:57.928903:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:57.928903:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:57.928903: RFID扫描: 发现 5 个标签
2025-08-26 16:35:57.928903: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:57.928903: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:57.929900: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.929900:   - UID: E004015304F3DD22
2025-08-26 16:35:57.929900:   - Data: E004015304F3DD22
2025-08-26 16:35:57.929900:   - EventType: 1
2025-08-26 16:35:57.929900:   - Direction: 0
2025-08-26 16:35:57.929900:   - Antenna: 1
2025-08-26 16:35:57.929900:   - TagFrequency: 0
2025-08-26 16:35:57.929900: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.930897:   - UID: E004015305F68508
2025-08-26 16:35:57.930897:   - Data: E004015305F68508
2025-08-26 16:35:57.930897:   - EventType: 1
2025-08-26 16:35:57.930897:   - Direction: 0
2025-08-26 16:35:57.930897:   - Antenna: 1
2025-08-26 16:35:57.930897:   - TagFrequency: 0
2025-08-26 16:35:57.930897: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.930897:   - UID: E004015304F3DD22
2025-08-26 16:35:57.931893:   - Data: E004015304F3DD22
2025-08-26 16:35:57.931893:   - EventType: 1
2025-08-26 16:35:57.931893:   - Direction: 0
2025-08-26 16:35:57.931893:   - Antenna: 1
2025-08-26 16:35:57.931893:   - TagFrequency: 0
2025-08-26 16:35:57.931893: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.931893:   - UID: E004015305F68508
2025-08-26 16:35:57.931893:   - Data: E004015305F68508
2025-08-26 16:35:57.932890:   - EventType: 1
2025-08-26 16:35:57.932890:   - Direction: 0
2025-08-26 16:35:57.932890:   - Antenna: 1
2025-08-26 16:35:57.932890:   - TagFrequency: 0
2025-08-26 16:35:57.932890: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:57.932890:   - UID: E004015304F3DD22
2025-08-26 16:35:57.932890:   - Data: E004015304F3DD22
2025-08-26 16:35:57.932890:   - EventType: 1
2025-08-26 16:35:57.933887:   - Direction: 0
2025-08-26 16:35:57.933887:   - Antenna: 1
2025-08-26 16:35:57.933887:   - TagFrequency: 0
2025-08-26 16:35:58.419277: 🔄 开始RFID轮询检查...
2025-08-26 16:35:58.419277: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:58.419277: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:58.419277: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:58.420275: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:58.420275: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:58.420275: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:58.422267: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:58.423264:   - 设备句柄: 2424588958144
2025-08-26 16:35:58.423264:   - FetchRecords返回值: 0
2025-08-26 16:35:58.423264:   - 报告数量: 5
2025-08-26 16:35:58.423264:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.423264:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:58.423264:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.423264:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.424260:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.424260:   - 数据长度: 8
2025-08-26 16:35:58.424260:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:58.424260:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:58.424260:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.424260:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:58.425257:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.425257:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.425257:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.425257:   - 数据长度: 8
2025-08-26 16:35:58.425257:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:58.425257:   - 提取的UID: E004015305F68508
2025-08-26 16:35:58.425257:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.426254:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:58.426254:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.426254:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.426254:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.426254:   - 数据长度: 8
2025-08-26 16:35:58.426254:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:58.426254:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:58.426254:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.427251:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:58.427251:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.427251:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.427251:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.427251:   - 数据长度: 8
2025-08-26 16:35:58.427251:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:58.427251:   - 提取的UID: E004015305F68508
2025-08-26 16:35:58.427251:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.428247:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:58.428247:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.428247:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.428247:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.428247:   - 数据长度: 8
2025-08-26 16:35:58.428247:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:58.428247:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:58.428247: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:58.429244:   - 发现标签数量: 5
2025-08-26 16:35:58.429244:   - 标签详情:
2025-08-26 16:35:58.429244:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:58.429244:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:58.429244:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:58.429244:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:58.429244:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:58.429244: RFID扫描: 发现 5 个标签
2025-08-26 16:35:58.430241: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:58.430241: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:58.430241: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.430241:   - UID: E004015304F3DD22
2025-08-26 16:35:58.430241:   - Data: E004015304F3DD22
2025-08-26 16:35:58.430241:   - EventType: 1
2025-08-26 16:35:58.430241:   - Direction: 0
2025-08-26 16:35:58.430241:   - Antenna: 1
2025-08-26 16:35:58.431237:   - TagFrequency: 0
2025-08-26 16:35:58.431237: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.431237:   - UID: E004015305F68508
2025-08-26 16:35:58.431237:   - Data: E004015305F68508
2025-08-26 16:35:58.431237:   - EventType: 1
2025-08-26 16:35:58.431237:   - Direction: 0
2025-08-26 16:35:58.431237:   - Antenna: 1
2025-08-26 16:35:58.431237:   - TagFrequency: 0
2025-08-26 16:35:58.432234: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.432234:   - UID: E004015304F3DD22
2025-08-26 16:35:58.432234:   - Data: E004015304F3DD22
2025-08-26 16:35:58.432234:   - EventType: 1
2025-08-26 16:35:58.432234:   - Direction: 0
2025-08-26 16:35:58.432234:   - Antenna: 1
2025-08-26 16:35:58.432234:   - TagFrequency: 0
2025-08-26 16:35:58.432234: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.433231:   - UID: E004015305F68508
2025-08-26 16:35:58.433231:   - Data: E004015305F68508
2025-08-26 16:35:58.433231:   - EventType: 1
2025-08-26 16:35:58.433231:   - Direction: 0
2025-08-26 16:35:58.433231:   - Antenna: 1
2025-08-26 16:35:58.433231:   - TagFrequency: 0
2025-08-26 16:35:58.433231: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.433231:   - UID: E004015304F3DD22
2025-08-26 16:35:58.434228:   - Data: E004015304F3DD22
2025-08-26 16:35:58.434228:   - EventType: 1
2025-08-26 16:35:58.434228:   - Direction: 0
2025-08-26 16:35:58.434228:   - Antenna: 1
2025-08-26 16:35:58.434228:   - TagFrequency: 0
2025-08-26 16:35:58.919618: 🔄 开始RFID轮询检查...
2025-08-26 16:35:58.919618: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:58.919618: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:58.919618: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:58.920615: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:58.920615: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:58.920615: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:58.922608: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:58.923605:   - 设备句柄: 2424588958144
2025-08-26 16:35:58.923605:   - FetchRecords返回值: 0
2025-08-26 16:35:58.923605:   - 报告数量: 5
2025-08-26 16:35:58.923605:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.923605:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:58.923605:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.923605:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.923605:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.924601:   - 数据长度: 8
2025-08-26 16:35:58.924601:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:58.924601:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:58.924601:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.924601:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:58.924601:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.924601:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.925598:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.925598:   - 数据长度: 8
2025-08-26 16:35:58.925598:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:58.925598:   - 提取的UID: E004015305F68508
2025-08-26 16:35:58.925598:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.925598:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:58.925598:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.925598:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.926595:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.926595:   - 数据长度: 8
2025-08-26 16:35:58.926595:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:58.926595:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:58.926595:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.926595:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:58.926595:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.926595:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.927591:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.927591:   - 数据长度: 8
2025-08-26 16:35:58.927591:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:58.927591:   - 提取的UID: E004015305F68508
2025-08-26 16:35:58.927591:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:58.927591:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:58.927591:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:58.927591:   - 设备类型: LSGControlCenter
2025-08-26 16:35:58.928588:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:58.928588:   - 数据长度: 8
2025-08-26 16:35:58.928588:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:58.928588:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:58.928588: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:58.928588:   - 发现标签数量: 5
2025-08-26 16:35:58.928588:   - 标签详情:
2025-08-26 16:35:58.928588:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:58.929585:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:58.929585:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:58.929585:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:58.929585:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:58.929585: RFID扫描: 发现 5 个标签
2025-08-26 16:35:58.929585: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:58.929585: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:58.929585: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.930582:   - UID: E004015304F3DD22
2025-08-26 16:35:58.930582:   - Data: E004015304F3DD22
2025-08-26 16:35:58.930582:   - EventType: 1
2025-08-26 16:35:58.930582:   - Direction: 0
2025-08-26 16:35:58.930582:   - Antenna: 1
2025-08-26 16:35:58.930582:   - TagFrequency: 0
2025-08-26 16:35:58.930582: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.930582:   - UID: E004015305F68508
2025-08-26 16:35:58.931578:   - Data: E004015305F68508
2025-08-26 16:35:58.931578:   - EventType: 1
2025-08-26 16:35:58.931578:   - Direction: 0
2025-08-26 16:35:58.931578:   - Antenna: 1
2025-08-26 16:35:58.931578:   - TagFrequency: 0
2025-08-26 16:35:58.931578: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.931578:   - UID: E004015304F3DD22
2025-08-26 16:35:58.931578:   - Data: E004015304F3DD22
2025-08-26 16:35:58.932575:   - EventType: 1
2025-08-26 16:35:58.932575:   - Direction: 0
2025-08-26 16:35:58.932575:   - Antenna: 1
2025-08-26 16:35:58.932575:   - TagFrequency: 0
2025-08-26 16:35:58.932575: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.932575:   - UID: E004015305F68508
2025-08-26 16:35:58.932575:   - Data: E004015305F68508
2025-08-26 16:35:58.932575:   - EventType: 1
2025-08-26 16:35:58.933572:   - Direction: 0
2025-08-26 16:35:58.933572:   - Antenna: 1
2025-08-26 16:35:58.933572:   - TagFrequency: 0
2025-08-26 16:35:58.933572: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:58.933572:   - UID: E004015304F3DD22
2025-08-26 16:35:58.933572:   - Data: E004015304F3DD22
2025-08-26 16:35:58.933572:   - EventType: 1
2025-08-26 16:35:58.933572:   - Direction: 0
2025-08-26 16:35:58.933572:   - Antenna: 1
2025-08-26 16:35:58.934568:   - TagFrequency: 0
2025-08-26 16:35:59.419476: 🔄 开始RFID轮询检查...
2025-08-26 16:35:59.419476: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:59.419476: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:59.419476: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:59.420474: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:59.420474: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:59.420474: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:59.422467: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:59.423464:   - 设备句柄: 2424588958144
2025-08-26 16:35:59.423464:   - FetchRecords返回值: 0
2025-08-26 16:35:59.423464:   - 报告数量: 5
2025-08-26 16:35:59.424461:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.424461:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:59.424461:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.424461:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.424461:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.424461:   - 数据长度: 8
2025-08-26 16:35:59.425458:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:59.425458:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:59.425458:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.427452:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:59.427452:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.427452:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.428448:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.428448:   - 数据长度: 8
2025-08-26 16:35:59.428448:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:59.429445:   - 提取的UID: E004015305F68508
2025-08-26 16:35:59.429445:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.429445:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:59.430441:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.430441:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.430441:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.430441:   - 数据长度: 8
2025-08-26 16:35:59.430441:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:59.431437:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:59.431437:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.431437:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:59.431437:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.431437:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.432434:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.432434:   - 数据长度: 8
2025-08-26 16:35:59.432434:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:59.433432:   - 提取的UID: E004015305F68508
2025-08-26 16:35:59.433432:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.433432:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:59.433432:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.433432:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.434428:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.434428:   - 数据长度: 8
2025-08-26 16:35:59.434428:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:59.434428:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:59.435425: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:59.435425:   - 发现标签数量: 5
2025-08-26 16:35:59.435425:   - 标签详情:
2025-08-26 16:35:59.435425:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:59.436421:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:59.436421:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:59.436421:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:59.436421:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:59.437418: RFID扫描: 发现 5 个标签
2025-08-26 16:35:59.437418: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:59.438414: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:59.438414: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.438414:   - UID: E004015304F3DD22
2025-08-26 16:35:59.439415:   - Data: E004015304F3DD22
2025-08-26 16:35:59.439415:   - EventType: 1
2025-08-26 16:35:59.439415:   - Direction: 0
2025-08-26 16:35:59.440413:   - Antenna: 1
2025-08-26 16:35:59.440413:   - TagFrequency: 0
2025-08-26 16:35:59.440413: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.440413:   - UID: E004015305F68508
2025-08-26 16:35:59.441405:   - Data: E004015305F68508
2025-08-26 16:35:59.441405:   - EventType: 1
2025-08-26 16:35:59.441405:   - Direction: 0
2025-08-26 16:35:59.441405:   - Antenna: 1
2025-08-26 16:35:59.441405:   - TagFrequency: 0
2025-08-26 16:35:59.441405: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.442401:   - UID: E004015304F3DD22
2025-08-26 16:35:59.442401:   - Data: E004015304F3DD22
2025-08-26 16:35:59.442401:   - EventType: 1
2025-08-26 16:35:59.442401:   - Direction: 0
2025-08-26 16:35:59.442401:   - Antenna: 1
2025-08-26 16:35:59.442401:   - TagFrequency: 0
2025-08-26 16:35:59.442401: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.442401:   - UID: E004015305F68508
2025-08-26 16:35:59.443398:   - Data: E004015305F68508
2025-08-26 16:35:59.443398:   - EventType: 1
2025-08-26 16:35:59.443398:   - Direction: 0
2025-08-26 16:35:59.443398:   - Antenna: 1
2025-08-26 16:35:59.443398:   - TagFrequency: 0
2025-08-26 16:35:59.443398: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.444395:   - UID: E004015304F3DD22
2025-08-26 16:35:59.444395:   - Data: E004015304F3DD22
2025-08-26 16:35:59.444395:   - EventType: 1
2025-08-26 16:35:59.444395:   - Direction: 0
2025-08-26 16:35:59.444395:   - Antenna: 1
2025-08-26 16:35:59.445392:   - TagFrequency: 0
2025-08-26 16:35:59.918347: 🔄 开始RFID轮询检查...
2025-08-26 16:35:59.918347: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:59.918347: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:59.918347: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:59.918347: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:59.919344: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:59.919344: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:59.921337: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:59.922334:   - 设备句柄: 2424588958144
2025-08-26 16:35:59.922334:   - FetchRecords返回值: 0
2025-08-26 16:35:59.922334:   - 报告数量: 5
2025-08-26 16:35:59.922334:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.922334:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:59.922334:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.922334:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.923331:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.923331:   - 数据长度: 8
2025-08-26 16:35:59.923331:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:59.923331:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:59.923331:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.923331:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:59.923331:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.924327:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.924327:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.924327:   - 数据长度: 8
2025-08-26 16:35:59.924327:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:59.924327:   - 提取的UID: E004015305F68508
2025-08-26 16:35:59.924327:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.924327:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:59.925324:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.925324:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.925324:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.925324:   - 数据长度: 8
2025-08-26 16:35:59.925324:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:59.925324:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:59.925324:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.925324:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:35:59.926321:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.926321:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.926321:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.926321:   - 数据长度: 8
2025-08-26 16:35:59.926321:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:35:59.926321:   - 提取的UID: E004015305F68508
2025-08-26 16:35:59.926321:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:59.926321:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:59.927317:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:35:59.927317:   - 设备类型: LSGControlCenter
2025-08-26 16:35:59.927317:   - 事件类型: 1, 方向: 0
2025-08-26 16:35:59.927317:   - 数据长度: 8
2025-08-26 16:35:59.927317:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:35:59.927317:   - 提取的UID: E004015304F3DD22
2025-08-26 16:35:59.927317: 📊 LSGate扫描结果汇总:
2025-08-26 16:35:59.927317:   - 发现标签数量: 5
2025-08-26 16:35:59.928314:   - 标签详情:
2025-08-26 16:35:59.928314:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:59.928314:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:59.928314:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:59.928314:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:35:59.928314:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:35:59.928314: RFID扫描: 发现 5 个标签
2025-08-26 16:35:59.928314: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:35:59.929311: 🎉 LSGate检测到RFID标签！
2025-08-26 16:35:59.929311: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.929311:   - UID: E004015304F3DD22
2025-08-26 16:35:59.929311:   - Data: E004015304F3DD22
2025-08-26 16:35:59.929311:   - EventType: 1
2025-08-26 16:35:59.929311:   - Direction: 0
2025-08-26 16:35:59.929311:   - Antenna: 1
2025-08-26 16:35:59.929311:   - TagFrequency: 0
2025-08-26 16:35:59.930308: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.930308:   - UID: E004015305F68508
2025-08-26 16:35:59.930308:   - Data: E004015305F68508
2025-08-26 16:35:59.930308:   - EventType: 1
2025-08-26 16:35:59.930308:   - Direction: 0
2025-08-26 16:35:59.930308:   - Antenna: 1
2025-08-26 16:35:59.930308:   - TagFrequency: 0
2025-08-26 16:35:59.930308: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.931304:   - UID: E004015304F3DD22
2025-08-26 16:35:59.931304:   - Data: E004015304F3DD22
2025-08-26 16:35:59.931304:   - EventType: 1
2025-08-26 16:35:59.931304:   - Direction: 0
2025-08-26 16:35:59.931304:   - Antenna: 1
2025-08-26 16:35:59.932302:   - TagFrequency: 0
2025-08-26 16:35:59.932302: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.932302:   - UID: E004015305F68508
2025-08-26 16:35:59.933306:   - Data: E004015305F68508
2025-08-26 16:35:59.933306:   - EventType: 1
2025-08-26 16:35:59.933306:   - Direction: 0
2025-08-26 16:35:59.934188:   - Antenna: 1
2025-08-26 16:35:59.934296:   - TagFrequency: 0
2025-08-26 16:35:59.934296: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:35:59.934296:   - UID: E004015304F3DD22
2025-08-26 16:35:59.934296:   - Data: E004015304F3DD22
2025-08-26 16:35:59.935294:   - EventType: 1
2025-08-26 16:35:59.935294:   - Direction: 0
2025-08-26 16:35:59.935294:   - Antenna: 1
2025-08-26 16:35:59.935294:   - TagFrequency: 0
2025-08-26 16:36:00.418688: 🔄 开始RFID轮询检查...
2025-08-26 16:36:00.418688: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:36:00.418688: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:36:00.419685: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:36:00.419685: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:36:00.419685: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:36:00.419685: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:36:00.421678: 🔍 LSGate硬件扫描详情:
2025-08-26 16:36:00.422675:   - 设备句柄: 2424588958144
2025-08-26 16:36:00.422675:   - FetchRecords返回值: 0
2025-08-26 16:36:00.422675:   - 报告数量: 5
2025-08-26 16:36:00.422675:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.422675:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:00.422675:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.422675:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.423672:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.423672:   - 数据长度: 8
2025-08-26 16:36:00.423672:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:00.423672:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:00.423672:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.423672:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:36:00.423672:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.424668:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.424668:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.424668:   - 数据长度: 8
2025-08-26 16:36:00.424668:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:36:00.424668:   - 提取的UID: E004015305F68508
2025-08-26 16:36:00.424668:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.424668:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:00.425665:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.425665:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.425665:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.425665:   - 数据长度: 8
2025-08-26 16:36:00.425665:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:00.425665:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:00.425665:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.425665:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:36:00.426662:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.426662:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.426662:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.426662:   - 数据长度: 8
2025-08-26 16:36:00.426662:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:36:00.426662:   - 提取的UID: E004015305F68508
2025-08-26 16:36:00.426662:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.426662:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:00.427658:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.427658:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.427658:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.427658:   - 数据长度: 8
2025-08-26 16:36:00.427658:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:00.427658:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:00.427658: 📊 LSGate扫描结果汇总:
2025-08-26 16:36:00.427658:   - 发现标签数量: 5
2025-08-26 16:36:00.428655:   - 标签详情:
2025-08-26 16:36:00.428655:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:00.428655:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:36:00.428655:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:00.428655:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:36:00.428655:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:00.428655: RFID扫描: 发现 5 个标签
2025-08-26 16:36:00.428655: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:36:00.429652: 🎉 LSGate检测到RFID标签！
2025-08-26 16:36:00.429652: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.429652:   - UID: E004015304F3DD22
2025-08-26 16:36:00.429652:   - Data: E004015304F3DD22
2025-08-26 16:36:00.429652:   - EventType: 1
2025-08-26 16:36:00.429652:   - Direction: 0
2025-08-26 16:36:00.429652:   - Antenna: 1
2025-08-26 16:36:00.429652:   - TagFrequency: 0
2025-08-26 16:36:00.430648: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.430648:   - UID: E004015305F68508
2025-08-26 16:36:00.430648:   - Data: E004015305F68508
2025-08-26 16:36:00.430648:   - EventType: 1
2025-08-26 16:36:00.430648:   - Direction: 0
2025-08-26 16:36:00.430648:   - Antenna: 1
2025-08-26 16:36:00.430648:   - TagFrequency: 0
2025-08-26 16:36:00.430648: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.431645:   - UID: E004015304F3DD22
2025-08-26 16:36:00.431645:   - Data: E004015304F3DD22
2025-08-26 16:36:00.431645:   - EventType: 1
2025-08-26 16:36:00.431645:   - Direction: 0
2025-08-26 16:36:00.431645:   - Antenna: 1
2025-08-26 16:36:00.431645:   - TagFrequency: 0
2025-08-26 16:36:00.431645: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.431645:   - UID: E004015305F68508
2025-08-26 16:36:00.432642:   - Data: E004015305F68508
2025-08-26 16:36:00.432642:   - EventType: 1
2025-08-26 16:36:00.432642:   - Direction: 0
2025-08-26 16:36:00.432642:   - Antenna: 1
2025-08-26 16:36:00.432642:   - TagFrequency: 0
2025-08-26 16:36:00.432642: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.432642:   - UID: E004015304F3DD22
2025-08-26 16:36:00.432642:   - Data: E004015304F3DD22
2025-08-26 16:36:00.433638:   - EventType: 1
2025-08-26 16:36:00.433638:   - Direction: 0
2025-08-26 16:36:00.433638:   - Antenna: 1
2025-08-26 16:36:00.433638:   - TagFrequency: 0
2025-08-26 16:36:00.919091: 🔄 开始RFID轮询检查...
2025-08-26 16:36:00.919091: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:36:00.919091: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:36:00.919091: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:36:00.919091: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:36:00.919091: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:36:00.920088: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:36:00.922081: 🔍 LSGate硬件扫描详情:
2025-08-26 16:36:00.923078:   - 设备句柄: 2424588958144
2025-08-26 16:36:00.923078:   - FetchRecords返回值: 0
2025-08-26 16:36:00.923078:   - 报告数量: 5
2025-08-26 16:36:00.923078:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.923078:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:00.923078:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.923078:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.924075:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.924075:   - 数据长度: 8
2025-08-26 16:36:00.924075:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:00.924075:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:00.924075:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.924075:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:36:00.924075:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.924075:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.925071:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.925071:   - 数据长度: 8
2025-08-26 16:36:00.925071:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:36:00.925071:   - 提取的UID: E004015305F68508
2025-08-26 16:36:00.925071:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.925071:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:00.925071:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.926068:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.926068:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.926068:   - 数据长度: 8
2025-08-26 16:36:00.926068:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:00.926068:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:00.926068:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.926068:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:36:00.926068:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.927065:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.927065:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.927065:   - 数据长度: 8
2025-08-26 16:36:00.927065:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:36:00.927065:   - 提取的UID: E004015305F68508
2025-08-26 16:36:00.927065:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:00.927065:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:00.927065:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:00.928061:   - 设备类型: LSGControlCenter
2025-08-26 16:36:00.928061:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:00.928061:   - 数据长度: 8
2025-08-26 16:36:00.928061:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:00.928061:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:00.928061: 📊 LSGate扫描结果汇总:
2025-08-26 16:36:00.928061:   - 发现标签数量: 5
2025-08-26 16:36:00.928061:   - 标签详情:
2025-08-26 16:36:00.929058:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:00.929058:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:36:00.929058:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:00.929058:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:36:00.929058:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:00.929058: RFID扫描: 发现 5 个标签
2025-08-26 16:36:00.929058: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:36:00.929058: 🎉 LSGate检测到RFID标签！
2025-08-26 16:36:00.930055: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.930055:   - UID: E004015304F3DD22
2025-08-26 16:36:00.930055:   - Data: E004015304F3DD22
2025-08-26 16:36:00.930055:   - EventType: 1
2025-08-26 16:36:00.930055:   - Direction: 0
2025-08-26 16:36:00.930055:   - Antenna: 1
2025-08-26 16:36:00.930055:   - TagFrequency: 0
2025-08-26 16:36:00.930055: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.931051:   - UID: E004015305F68508
2025-08-26 16:36:00.931051:   - Data: E004015305F68508
2025-08-26 16:36:00.931051:   - EventType: 1
2025-08-26 16:36:00.931051:   - Direction: 0
2025-08-26 16:36:00.931051:   - Antenna: 1
2025-08-26 16:36:00.931051:   - TagFrequency: 0
2025-08-26 16:36:00.931051: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.931051:   - UID: E004015304F3DD22
2025-08-26 16:36:00.932048:   - Data: E004015304F3DD22
2025-08-26 16:36:00.932048:   - EventType: 1
2025-08-26 16:36:00.932048:   - Direction: 0
2025-08-26 16:36:00.932048:   - Antenna: 1
2025-08-26 16:36:00.932048:   - TagFrequency: 0
2025-08-26 16:36:00.932048: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.932048:   - UID: E004015305F68508
2025-08-26 16:36:00.932048:   - Data: E004015305F68508
2025-08-26 16:36:00.933045:   - EventType: 1
2025-08-26 16:36:00.933045:   - Direction: 0
2025-08-26 16:36:00.933045:   - Antenna: 1
2025-08-26 16:36:00.933045:   - TagFrequency: 0
2025-08-26 16:36:00.933045: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:00.933045:   - UID: E004015304F3DD22
2025-08-26 16:36:00.933045:   - Data: E004015304F3DD22
2025-08-26 16:36:00.933045:   - EventType: 1
2025-08-26 16:36:00.934042:   - Direction: 0
2025-08-26 16:36:00.934042:   - Antenna: 1
2025-08-26 16:36:00.934042:   - TagFrequency: 0
2025-08-26 16:36:01.207136: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-26 16:36:01.208133: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-26 16:36:01.208133: 🔍 数据长度: 8 字节
2025-08-26 16:36:01.208133: 🔍 预定义命令列表:
2025-08-26 16:36:01.208133:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:36:01.208133:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:36:01.209130:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:36:01.209130:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:36:01.209130:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:36:01.209130:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:36:01.209130:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:36:01.209130:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:36:01.209130:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:36:01.209130:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:36:01.210127: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-26 16:36:01.210127: 解析到闸机命令: exit_end (出馆结束)
2025-08-26 16:36:01.210127: 收到闸机命令: exit_end (出馆结束)
2025-08-26 16:36:01.211134: 出馆流程结束
2025-08-26 16:36:01.211134: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-26 16:36:01.211770: [channel_1] 收到闸机事件: exit_end
2025-08-26 16:36:01.212120: [channel_1] 主从机扩展：处理出馆结束
2025-08-26 16:36:01.212120: [channel_1] 清空处理队列，当前大小: 0
2025-08-26 16:36:01.212120: [channel_1] 处理队列已清空
2025-08-26 16:36:01.212120: 📨 收到GateCoordinator事件: exit_end
2025-08-26 16:36:01.212120: 页面状态变更: SilencePageState.welcome
2025-08-26 16:36:01.212120: [channel_1] 通知收集到的条码: []
2025-08-26 16:36:01.212120: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-26 16:36:01.419435: 🔄 开始RFID轮询检查...
2025-08-26 16:36:01.420430: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:36:01.420430: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:36:01.420430: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:36:01.421433: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:36:01.421433: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:36:01.421433: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:36:01.423419: 🔍 LSGate硬件扫描详情:
2025-08-26 16:36:01.423419:   - 设备句柄: 2424588958144
2025-08-26 16:36:01.424416:   - FetchRecords返回值: 0
2025-08-26 16:36:01.424416:   - 报告数量: 5
2025-08-26 16:36:01.424416:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:01.424416:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:01.424416:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:01.424416:   - 设备类型: LSGControlCenter
2025-08-26 16:36:01.425413:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:01.425413:   - 数据长度: 8
2025-08-26 16:36:01.425413:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:01.425413:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:01.425413:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:01.425413:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:36:01.426409:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:01.426409:   - 设备类型: LSGControlCenter
2025-08-26 16:36:01.426409:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:01.426409:   - 数据长度: 8
2025-08-26 16:36:01.426409:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:36:01.426409:   - 提取的UID: E004015305F68508
2025-08-26 16:36:01.426409:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:01.427405:   - 原始数据: 01 00 19 08 1A 10 24 36 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:01.427405:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:01.427405:   - 设备类型: LSGControlCenter
2025-08-26 16:36:01.427405:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:01.427405:   - 数据长度: 8
2025-08-26 16:36:01.427405:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:01.427405:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:01.427405:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:01.428402:   - 原始数据: 01 00 19 08 1A 10 24 37 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:36:01.428402:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:01.428402:   - 设备类型: LSGControlCenter
2025-08-26 16:36:01.428402:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:01.428402:   - 数据长度: 8
2025-08-26 16:36:01.428402:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:36:01.428402:   - 提取的UID: E004015305F68508
2025-08-26 16:36:01.428402:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:36:01.429399:   - 原始数据: 01 00 19 08 1A 10 24 38 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:36:01.429399:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:36:01.429399:   - 设备类型: LSGControlCenter
2025-08-26 16:36:01.429399:   - 事件类型: 1, 方向: 0
2025-08-26 16:36:01.429399:   - 数据长度: 8
2025-08-26 16:36:01.429399:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:36:01.429399:   - 提取的UID: E004015304F3DD22
2025-08-26 16:36:01.430396: 📊 LSGate扫描结果汇总:
2025-08-26 16:36:01.430396:   - 发现标签数量: 5
2025-08-26 16:36:01.430396:   - 标签详情:
2025-08-26 16:36:01.430396:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:01.430396:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:36:01.430396:     [2] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:01.430396:     [3] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:36:01.430396:     [4] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:36:01.431392: RFID扫描: 发现 5 个标签
2025-08-26 16:36:01.431392: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:36:01.431392: 🎉 LSGate检测到RFID标签！
2025-08-26 16:36:01.431392: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 53], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:01.431392:   - UID: E004015304F3DD22
2025-08-26 16:36:01.431392:   - Data: E004015304F3DD22
2025-08-26 16:36:01.431392:   - EventType: 1
2025-08-26 16:36:01.432389:   - Direction: 0
2025-08-26 16:36:01.432389:   - Antenna: 1
2025-08-26 16:36:01.432389:   - TagFrequency: 0
2025-08-26 16:36:01.432389: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:01.432389:   - UID: E004015305F68508
2025-08-26 16:36:01.432389:   - Data: E004015305F68508
2025-08-26 16:36:01.432389:   - EventType: 1
2025-08-26 16:36:01.432389:   - Direction: 0
2025-08-26 16:36:01.432389:   - Antenna: 1
2025-08-26 16:36:01.433386:   - TagFrequency: 0
2025-08-26 16:36:01.433386: 🏷️ LSGate UID[2]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 54], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:01.433386:   - UID: E004015304F3DD22
2025-08-26 16:36:01.433386:   - Data: E004015304F3DD22
2025-08-26 16:36:01.433386:   - EventType: 1
2025-08-26 16:36:01.433386:   - Direction: 0
2025-08-26 16:36:01.433386:   - Antenna: 1
2025-08-26 16:36:01.433386:   - TagFrequency: 0
2025-08-26 16:36:01.434385: 🏷️ LSGate UID[3]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 55], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:01.434385:   - UID: E004015305F68508
2025-08-26 16:36:01.434385:   - Data: E004015305F68508
2025-08-26 16:36:01.434385:   - EventType: 1
2025-08-26 16:36:01.434385:   - Direction: 0
2025-08-26 16:36:01.434385:   - Antenna: 1
2025-08-26 16:36:01.434385:   - TagFrequency: 0
2025-08-26 16:36:01.434385: 🏷️ LSGate UID[4]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 36, 56], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:36:01.435379:   - UID: E004015304F3DD22
2025-08-26 16:36:01.435379:   - Data: E004015304F3DD22
2025-08-26 16:36:01.435379:   - EventType: 1
2025-08-26 16:36:01.435379:   - Direction: 0
2025-08-26 16:36:01.435379:   - Antenna: 1
2025-08-26 16:36:01.435379:   - TagFrequency: 0
